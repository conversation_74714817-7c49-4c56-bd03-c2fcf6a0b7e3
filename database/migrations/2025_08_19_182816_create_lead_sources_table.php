<?php

use App\Models\LeadSource;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('lead_sources', function (Blueprint $table) {
            $table->id();
            $table->string('name')->nullable();
            $table->foreignId('company_id')->nullable()->constrained()->nullOnDelete();
            $table->unsignedBigInteger('incremental_id')->index()->nullable();
            $table->softDeletes();
            $table->timestamps();
        });

        LeadSource::query()->create([
            'name' => 'Website',
            'company_id' => 1,
            'incremental_id' => 1,
        ]);

        LeadSource::query()->create([
            'name' => 'Email',
            'company_id' => 1,
            'incremental_id' => 2,
        ]);

        LeadSource::query()->create([
            'name' => 'Phone',
            'company_id' => 1,
            'incremental_id' => 3,
        ]);

        LeadSource::query()->create([
            'name' => 'Social Media',
            'company_id' => 1,
            'incremental_id' => 5,
        ]);

        LeadSource::query()->create([
            'name' => 'Referral',
            'company_id' => 1,
            'incremental_id' => 6,
        ]);

        LeadSource::query()->create([
            'name' => 'Other',
            'company_id' => 1,
            'incremental_id' => 4,
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('lead_sources');
    }
};
