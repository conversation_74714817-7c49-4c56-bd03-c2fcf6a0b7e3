<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('follow_up_types', function (Blueprint $table) {
            $table->id();
            $table->string('name')->nullable();
            $table->foreignId('company_id')->nullable()->constrained()->nullOnDelete();
            $table->unsignedBigInteger('incremental_id')->index()->nullable();
            $table->softDeletes();
            $table->timestamps();
        });

        \App\Models\FollowUpType::query()->create([
            'name' => 'Call',
            'company_id' => 1,
            'incremental_id' => 1,
        ]);

        \App\Models\FollowUpType::query()->create([
            'name' => 'Email',
            'company_id' => 1,
            'incremental_id' => 2,
        ]);

        \App\Models\FollowUpType::query()->create([
            'name' => 'Meeting',
            'company_id' => 1,
            'incremental_id' => 3,
        ]);

        \App\Models\FollowUpType::query()->create([
            'name' => 'Other',
            'company_id' => 1,
            'incremental_id' => 4,
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('follow_up_types');
    }
};
