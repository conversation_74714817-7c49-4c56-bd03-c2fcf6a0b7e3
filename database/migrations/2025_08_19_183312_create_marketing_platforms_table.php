<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('marketing_platforms', function (Blueprint $table) {
            $table->id();
            $table->string('name')->nullable();
            $table->foreignId('company_id')->nullable()->constrained()->nullOnDelete();
            $table->unsignedBigInteger('incremental_id')->index()->nullable();
            $table->softDeletes();
            $table->timestamps();
        });

        \App\Models\MarketingPlatform::query()->create([
            'name' => 'Facebook',
            'company_id' => 1,
            'incremental_id' => 1,
        ]);

        \App\Models\MarketingPlatform::query()->create([
            'name' => 'Instagram',
            'company_id' => 1,
            'incremental_id' => 2,
        ]);

        \App\Models\MarketingPlatform::query()->create([
            'name' => 'LinkedIn',
            'company_id' => 1,
            'incremental_id' => 3,
        ]);

        \App\Models\MarketingPlatform::query()->create([
            'name' => 'Twitter',
            'company_id' => 1,
            'incremental_id' => 4,
        ]);

        \App\Models\MarketingPlatform::query()->create([
            'name' => 'Other',
            'company_id' => 1,
            'incremental_id' => 5,
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('marketing_platforms');
    }
};
