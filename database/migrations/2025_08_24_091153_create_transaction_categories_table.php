<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('transaction_categories', function (Blueprint $table) {
            $table->id();
            $table->text('name')->nullable();
            $table->foreignId('company_id')->nullable()->constrained()->nullOnDelete();
            $table->unsignedBigInteger('incremental_id')->index()->nullable();
            $table->softDeletes();
            $table->timestamps();
        });

        \App\Models\TransactionCategory::query()->create([
            'name' => [
                'en' => 'Sales',
                'ar' => 'مبيعات',
            ],
        ]);

        \App\Models\TransactionCategory::query()->create([
            'name' => [
                'en' => 'Purchases',
                'ar' => 'مشتريات',
            ],
        ]);

        \App\Models\TransactionCategory::query()->create([
            'name' => [
                'en' => 'Services',
                'ar' => 'خدمات',
            ],
        ]);

        \App\Models\TransactionCategory::query()->create([
            'name' => [
                'en' => 'Other',
                'ar' => 'أخرى',
            ],
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('transaction_categories');
    }
};
