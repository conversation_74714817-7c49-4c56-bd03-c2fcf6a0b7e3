<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payments', function (Blueprint $table) {
            $table->id();
            $table->nullableMorphs('payable');
            $table->nullableMorphs('party');
            $table->nullableMorphs('creator');
            $table->string('transaction_type')->nullable();
            $table->double('amount')->nullable();
            $table->string('amount_currency')->nullable();
            $table->date('due_date')->nullable();
            $table->date('paid_at')->nullable();
            $table->text('attachments')->nullable();
            $table->text('notes')->nullable();
            $table->foreignId('payment_method_id')->nullable()->constrained()->nullOnDelete();
            $table->foreignId('company_id')->nullable()->constrained()->nullOnDelete();
            $table->unsignedBigInteger('incremental_id')->index()->nullable();
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payments');
    }
};
