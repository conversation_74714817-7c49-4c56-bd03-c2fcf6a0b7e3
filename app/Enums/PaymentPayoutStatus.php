<?php

namespace App\Enums;

use App\Enums\Concerns\Common;

enum PaymentPayoutStatus: string
{
    use Common;
    case FULLY_PAID = 'fully_paid';
    case PARTIALLY_PAID = 'partially_paid';

    case NO_PAYMENT = 'no_payment';

    public function label(): string
    {
        return match ($this) {
            self::FULLY_PAID => __('Fully Paid'),
            self::PARTIALLY_PAID => __('Partially Paid'),
            self::NO_PAYMENT => __('No Payment'),
        };
    }

    public function color(): string
    {
        return match ($this) {
            self::FULLY_PAID => 'success',
            self::PARTIALLY_PAID => 'warning',
            self::NO_PAYMENT => 'danger',
        };
    }
}
