<?php

namespace App\Enums;

use App\Enums\Concerns\Common;
use function Symfony\Component\String\s;

enum MarketingActivityAction: string
{
    use Common;

    case PENDING = 'pending';
    case APPROVED = 'approved';

    case DESIGNED = 'designed';

    case PUBLISHED = 'published';

    case CANCELLED = 'cancelled';

    public function label(): string
    {
        return match ($this) {
            self::PENDING => __('Pending'),
            self::APPROVED => __('Approved'),
            self::DESIGNED => __('Designed'),
            self::PUBLISHED => __('Published'),
            self::CANCELLED => __('Cancelled'),
        };
    }

    public function color(): string
    {
        return match ($this) {
            self::PENDING => 'gray',
            self::APPROVED => 'warning',
            self::DESIGNED => 'success',
            self::PUBLISHED => 'primary',
            self::CANCELLED => 'danger',
        };
    }
}
