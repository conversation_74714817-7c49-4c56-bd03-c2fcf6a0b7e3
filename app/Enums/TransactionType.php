<?php

namespace App\Enums;

use App\Enums\Concerns\Common;

enum TransactionType: string
{
    use Common;

    case DEPOSIT = 'deposit';

    case WITHDRAWAL = 'withdrawal';

    public function label(): string
    {
        return match ($this) {
            self::DEPOSIT => __('Deposit'),
            self::WITHDRAWAL => __('Withdrawal'),
        };
    }

    public function color(): string
    {
        return match ($this) {
            self::DEPOSIT => 'success',
            self::WITHDRAWAL => 'danger',
        };
    }
}
