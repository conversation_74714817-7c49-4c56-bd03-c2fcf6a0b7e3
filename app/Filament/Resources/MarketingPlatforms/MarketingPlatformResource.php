<?php

namespace App\Filament\Resources\MarketingPlatforms;

use App\Filament\Resources\MarketingPlatforms\Pages\CreateMarketingPlatform;
use App\Filament\Resources\MarketingPlatforms\Pages\EditMarketingPlatform;
use App\Filament\Resources\MarketingPlatforms\Pages\ListMarketingPlatforms;
use App\Filament\Resources\MarketingPlatforms\RelationManagers\MarketingActivitiesRelationManager;
use App\Filament\Resources\MarketingPlatforms\Schemas\MarketingPlatformForm;
use App\Filament\Resources\MarketingPlatforms\Tables\MarketingPlatformsTable;
use App\Models\MarketingPlatform;
use BackedEnum;
use App\Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use UnitEnum;

class MarketingPlatformResource extends Resource
{
    protected static ?string $model = MarketingPlatform::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedRectangleStack;

    public static function getLabel(): ?string
    {
        return __('Marketing Platform');
    }

    public static function getPluralLabel(): ?string
    {
        return __('Marketing Platforms');
    }

    public static function getNavigationGroup(): string|UnitEnum|null
    {
        return __('Marketing');
    }

    public static function form(Schema $schema): Schema
    {
        return MarketingPlatformForm::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return MarketingPlatformsTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            MarketingActivitiesRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListMarketingPlatforms::route('/'),
            'create' => CreateMarketingPlatform::route('/create'),
            'edit' => EditMarketingPlatform::route('/{record}/edit'),
        ];
    }

    public static function getRecordRouteBindingEloquentQuery(): Builder
    {
        return parent::getRecordRouteBindingEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
