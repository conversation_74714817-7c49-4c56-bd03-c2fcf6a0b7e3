<?php

namespace App\Filament\Resources\PurchaseOrders\Schemas;

use App\Enums\OrderIncoterm;
use App\Enums\OrderModelOfShipping;
use App\Enums\OrderOrigin;
use App\Enums\TransactionType;
use App\Filament\Forms\Components\BelongsToSelect;
use App\Filament\Resources\Suppliers\Schemas\SupplierForm;
use App\Filament\Schemas\Components\CountryCitySelect;
use App\Filament\Schemas\Components\PriceField;
use App\Filament\Widgets\PaymentsTable;
use App\Models\Airport;
use App\Models\Supplier;
use App\Models\Product;
use App\Models\Seaport;
use App\Models\PurchaseOrderService;
use App\Models\ServiceProvider;
use App\Models\ServiceProviderItem;
use Filament\Actions\Action;
use Filament\Forms\Components\MorphToSelect;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\ToggleButtons;
use Filament\Infolists\Components\TextEntry;
use Filament\Notifications\Notification;
use Filament\Schemas\Components\Actions;
use Filament\Schemas\Components\Component;
use Filament\Schemas\Components\Grid;
use Filament\Schemas\Components\Group;
use Filament\Schemas\Components\Livewire;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Components\Tabs;
use Filament\Schemas\Components\Text;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Illuminate\Support\Str;

class PurchaseOrderForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->columns(1)
            ->components([
                // supplier
                Section::make()
                    ->schema([
                        BelongsToSelect::make('supplier_id')
                            ->label(__('Supplier'))
                            ->relationship('supplier', 'name')
                            ->preload()
                            ->searchable()
                            ->required()
                            ->reactive()
                            ->resourceForm(SupplierForm::class)
                            ->afterStateUpdated(function ($state, $set, $get, Component $component) {
                                if (!$state) {
                                    return;
                                }

                                $supplier = Supplier::query()->findOrFail($state);

                                // set payment term
                                $set('payment_term_id', $supplier->payment_term_id);

                                // reset products
                                $uuid = Str::uuid()->toString();
                                $set('purchaseOrderProducts', [
                                    $uuid => [
                                        'product_id' => null,
                                        'quantity' => 1,
                                        'buy_price' => null,
                                        'buy_price_currency' => 'EGP',
                                        'package_type_id' => null,
                                        'width' => null,
                                        'height' => null,
                                        'length' => null,
                                        'net_weight' => null,
                                        'gross_weight' => null,
                                    ],
                                ]);

                                // reset shipping
                                $set('model_of_shipping', OrderModelOfShipping::Sea->value);
                                $set('incoterm', OrderModelOfShipping::Sea->firstIncotermValue());

                                [$country, $city, $address] = static::getSupplierCountryAndCity($get, $set, $component);
//                                $port = static::getSupplierPort($get, $set, $component);

                                if ($country) {
                                    $set('pickup_country_id', $country->id);
                                }

                                if ($city) {
                                    $set('pickup_city_id', $city->id);
                                }

                                if ($address) {
                                    $set('pickup_address', $address);
                                }

//                                if ($port) {
//                                    $set('port_of_loading_type', $port->getMorphClass());
//                                    $set('port_of_loading_id', $port->id);
//
//                                    $set('port_of_discharge_type', $port->getMorphClass());
//                                    $set('port_of_discharge_id', $port->id);
//                                }
                            }),

                        Radio::make('origin')
                            ->label(__('Origin'))
                            ->options(fn() => OrderOrigin::getSelectOptions())
                            ->default(fn() => OrderOrigin::Local->value)
                            ->reactive()
                            ->afterStateUpdated(function ($state, $set, $get, Component $component) {
                                // reset products
                                $uuid = Str::uuid()->toString();
                                $set('purchaseOrderProducts', [
                                    $uuid => [
                                        'product_id' => null,
                                        'quantity' => 1,
                                        'buy_price' => null,
                                        'buy_price_currency' => 'EGP',
                                        'package_type_id' => null,
                                        'width' => null,
                                        'height' => null,
                                        'length' => null,
                                        'net_weight' => null,
                                        'gross_weight' => null,
                                    ],
                                ]);
                            })
                            ->required(),

                        BelongsToSelect::make('payment_term_id')
                            ->label(__('Payment Term'))
                            ->relationship('paymentTerm', 'name')
                            ->preload()
                            ->searchable()
                            ->required(),
                    ]),

                Tabs::make()
                    ->persistTabInQueryString()
                    ->tabs([
                        Tabs\Tab::make(__('Order Details'))
                            ->icon(Heroicon::InformationCircle)
                            ->schema([
                                // products
                                Section::make(__('Products'))
                                    ->schema([
                                        Repeater::make('purchaseOrderProducts')
                                            ->label(__('Products'))
                                            ->relationship('purchaseOrderProducts')
                                            ->columns(3)
                                            ->addActionLabel(__('Add Product'))
                                            ->deletable(function ($state) {
                                                return count($state) > 1;
                                            })
                                            ->schema([
                                                BelongsToSelect::make('product_id')
                                                    ->label(__('Product'))
                                                    ->relationship('product', 'name')
                                                    ->preload()
                                                    ->searchable()
                                                    ->reactive()
                                                    ->afterStateUpdated(function ($state, $set) {
                                                        if ($state) {
                                                            $product = Product::query()->findOrFail($state);
                                                            if ($product) {
                                                                $set('buy_price', $product->buy_price);
                                                                $set('buy_price_currency', $product->buy_price_currency);
                                                            }
                                                        }
                                                    })
                                                    ->required(),

                                                TextInput::make('quantity')
                                                    ->label(__('Quantity'))
                                                    ->numeric()
                                                    ->required()
                                                    ->minValue(1),

                                                PriceField::make()
                                                    ->priceFieldName('buy_price')
                                                    ->currencyFieldName('buy_price_currency')
                                                    ->priceFieldLabel(__('Buy Price (per unit)'))
                                                    ->currencyFieldLabel(__('Buy Price Currency'))
                                                    ->required(),

                                                Group::make([
                                                    Select::make('package_type_id')
                                                        ->label(__('Package Type'))
                                                        ->relationship('packageType', 'name'),

                                                    TextInput::make('width')
                                                        ->label(__('Width (cm)'))
                                                        ->numeric()
                                                        ->requiredWith(['height', 'length'])
                                                        ->minValue(1),

                                                    TextInput::make('height')
                                                        ->label(__('Height (cm)'))
                                                        ->numeric()
                                                        ->requiredWith(['width', 'length'])
                                                        ->minValue(1),

                                                    TextInput::make('length')
                                                        ->label(__('Length (cm)'))
                                                        ->numeric()
                                                        ->requiredWith(['width', 'height'])
                                                        ->minValue(1),

                                                    TextInput::make('net_weight')
                                                        ->label(__('Net Weight (kg)'))
                                                        ->numeric()
                                                        ->requiredWith(['gross_weight'])
                                                        ->minValue(1),

                                                    TextInput::make('gross_weight')
                                                        ->label(__('Gross Weight (kg)'))
                                                        ->numeric()
                                                        ->requiredWith(['net_weight'])
                                                        ->minValue(1),
                                                ])->columnSpan(3)
                                                    ->columns(3)
                                                    ->visible(function ($get) {
                                                        return $get('data.origin', true) == OrderOrigin::Foreign->value;
                                                    })
                                            ]),
                                    ]),

                                // shipping
                                Section::make(__('Shipping'))
                                    ->visible(function ($get) {
                                        return $get('origin') == OrderOrigin::Foreign->value;
                                    })
                                    ->schema([
                                        Radio::make('model_of_shipping')
                                            ->label(__('Model of Shipping'))
                                            ->options(fn() => OrderModelOfShipping::getSelectOptions())
                                            ->default(fn() => OrderModelOfShipping::Sea->value)
                                            ->reactive()
                                            ->afterStateUpdated(function ($state, $set) {
                                                if ($state == OrderModelOfShipping::Air->value) {
                                                    $set('port_of_loading_type', Airport::class);
                                                    $set('port_of_discharge_type', Airport::class);
                                                }
                                                if ($state == OrderModelOfShipping::Sea->value) {
                                                    $set('port_of_loading_type', Seaport::class);
                                                    $set('port_of_discharge_type', Seaport::class);
                                                }
                                                if ($state == OrderModelOfShipping::Courier->value) {
                                                    $set('port_of_loading_type', null);
                                                    $set('port_of_discharge_type', null);
                                                }
                                                $set('incoterm', OrderModelOfShipping::findByHandle($state)->firstIncotermValue());
                                            })
                                            ->required(),

                                        Radio::make('incoterm')
                                            ->label(__('Incoterm'))
                                            ->default(function ($get) {
                                                return OrderModelOfShipping::findByHandle($get('data.model_of_shipping', true))->firstIncotermValue();
                                            })
                                            ->options(function ($get) {
                                                return OrderModelOfShipping::findByHandle($get('data.model_of_shipping', true))->incotermSelectOptions();
                                            })
                                            ->reactive()
                                            ->afterStateUpdated(function ($set, $get, Component $component) {
                                                $modelOfShipping = $get('data.model_of_shipping', true);

                                                if ($modelOfShipping == OrderModelOfShipping::Air->value) {
                                                    $set('port_of_loading_type', Airport::class);
                                                    $set('port_of_discharge_type', Airport::class);
                                                } elseif ($modelOfShipping == OrderModelOfShipping::Sea->value) {
                                                    $set('port_of_loading_type', Seaport::class);
                                                    $set('port_of_discharge_type', Seaport::class);
                                                }

                                                [$country, $city, $address] = static::getSupplierCountryAndCity($get, $set, $component);
//                                                $port = static::getSupplierPort($get, $set, $component);

                                                if ($country) {
                                                    $set('pickup_country_id', $country->id);
                                                }

                                                if ($city) {
                                                    $set('pickup_city_id', $city->id);
                                                }

                                                if ($address) {
                                                    $set('pickup_address', $address);
                                                }

//                                                if ($port) {
//                                                    $set('port_of_loading_type', $port->getMorphClass());
//                                                    $set('port_of_loading_id', $port->id);
//
//                                                    $set('port_of_discharge_type', $port->getMorphClass());
//                                                    $set('port_of_discharge_id', $port->id);
//                                                }
                                            })
                                            ->required(),

                                        Section::make(__('Pickup'))
                                            ->schema([
                                                CountryCitySelect::make()
                                                    ->countryFieldName('pickup_country_id')
                                                    ->cityFieldName('pickup_city_id')
                                                    ->countryFieldLabel(__('Pickup Country'))
                                                    ->cityFieldLabel(__('Pickup City'))
                                                    ->countryRelationshipName('pickupCountry')
                                                    ->cityRelationshipName('pickupCity')
                                                    ->required(),

                                                Textarea::make('pickup_address')
                                                    ->label(__('Pickup Address'))
                                                    ->required(),
                                            ]),

                                        Section::make(__('Delivery'))
                                            ->schema([
                                                CountryCitySelect::make()
                                                    ->countryFieldName('delivery_country_id')
                                                    ->cityFieldName('delivery_city_id')
                                                    ->countryFieldLabel(__('Delivery Country'))
                                                    ->cityFieldLabel(__('Delivery City'))
                                                    ->countryRelationshipName('deliveryCountry')
                                                    ->cityRelationshipName('deliveryCity')
                                                    ->required(),


                                                Textarea::make('delivery_address')
                                                    ->label(__('Delivery Address'))
                                                    ->hidden(function ($get) {
                                                        $incoterm = $get('data.incoterm', true);

                                                        return $incoterm == OrderIncoterm::CIF->value || $incoterm == OrderIncoterm::FOB->value;
                                                    })
                                                    ->required(),

                                                MorphToSelect::make('portOfDischarge')
                                                    ->label(__('Port of Discharge'))
                                                    ->types(function ($get) {
                                                        $modelOfShipping = $get('data.model_of_shipping', true);

                                                        if ($modelOfShipping == OrderModelOfShipping::Air->value) {
                                                            return [
                                                                MorphToSelect\Type::make(Airport::class)
                                                                    ->titleAttribute('name'),
                                                            ];
                                                        } elseif ($modelOfShipping == OrderModelOfShipping::Sea->value) {
                                                            return [
                                                                MorphToSelect\Type::make(Seaport::class)
                                                                    ->titleAttribute('name'),
                                                            ];
                                                        }

                                                        return [];
                                                    })
                                                    ->visible(function ($get) {
                                                        $incoterm = $get('data.incoterm', true);

                                                        return $incoterm == OrderIncoterm::CIF->value;
                                                    })
                                                    ->required(),

                                                MorphToSelect::make('portOfLoading')
                                                    ->label(__('Port of Loading'))
                                                    ->preload()
                                                    ->searchable()
                                                    ->types(function ($get) {
                                                        $modelOfShipping = $get('data.model_of_shipping', true);

                                                        if ($modelOfShipping == OrderModelOfShipping::Air->value) {
                                                            return [
                                                                MorphToSelect\Type::make(Airport::class)
                                                                    ->titleAttribute('name'),
                                                            ];
                                                        }

                                                        if ($modelOfShipping == OrderModelOfShipping::Sea->value) {
                                                            return [
                                                                MorphToSelect\Type::make(Seaport::class)
                                                                    ->titleAttribute('name'),
                                                            ];
                                                        }

                                                        return [];
                                                    })
                                                    ->visible(function ($get) {
                                                        $incoterm = $get('data.incoterm', true);

                                                        return $incoterm == OrderIncoterm::FOB->value;
                                                    })
                                                    ->required(),
                                            ])
                                    ]),

                                // services
                            ]),

                        Tabs\Tab::make(__('Services'))
                            ->icon(Heroicon::Truck)
                            ->schema([
                                Repeater::make('purchaseOrderServices')
                                    ->label(__('Services'))
                                    ->relationship('purchaseOrderServices')
                                    ->columns(1)
                                    ->schema([
                                        Grid::make(3)
                                            ->schema([
                                                Group::make([
                                                    Grid::make(1)
                                                        ->schema([
                                                            ToggleButtons::make('service_id')
                                                                ->label(__('Service'))
                                                                ->options(function () {
                                                                    return \App\Models\Service::query()->pluck('name', 'id')->toArray();
                                                                })
                                                                ->inline()
                                                                ->reactive()
                                                                ->afterStateUpdated(function ($set) {
                                                                    $set('service_provider_id', null);
                                                                    $set('payment_term_id', null);

                                                                    $uuid = Str::uuid()->toString();
                                                                    $set('purchaseOrderServiceItems', [
                                                                        $uuid => [
                                                                            'service_item_id' => null,
                                                                            'price' => null,
                                                                            'price_currency' => 'EGP',
                                                                        ],
                                                                    ]);
                                                                })
                                                                ->required(),

                                                            BelongsToSelect::make('service_provider_id')
                                                                ->label(__('Service Provider'))
                                                                ->afterLabel(function ($get) {
                                                                    if (!$get('service_id')) {
                                                                        return Text::make(__('Please select a service first.'))
                                                                            ->color('danger');
                                                                    }

                                                                    return null;
                                                                })
                                                                ->disabled(function ($get) {
                                                                    return ! $get('service_id');
                                                                })
                                                                ->relationship('serviceProvider', 'name', function ($query, $get) {
                                                                    return $query->where('service_providers.service_id', $get('service_id'));
                                                                })
                                                                ->reactive()
                                                                ->afterStateUpdated(function ($get, $set) {
                                                                    $serviceProvider = ServiceProvider::query()->find($get('service_provider_id'));

                                                                    if ($serviceProvider) {
                                                                        $set('payment_term_id', $serviceProvider->payment_term_id);
                                                                    }
                                                                })
                                                                ->searchable()
                                                                ->preload()
                                                                ->required(),
                                                        ]),

                                                    Repeater::make('purchaseOrderServiceItems')
                                                        ->label(__('Service Items'))
                                                        ->relationship('purchaseOrderServiceItems')
                                                        ->visible(function ($get) {
                                                            return $get('service_id') && $get('service_provider_id');
                                                        })
                                                        ->columns(3)
                                                        ->table([
                                                            Repeater\TableColumn::make(__('Service Item')),

                                                            Repeater\TableColumn::make(__('Price')),

                                                            Repeater\TableColumn::make(__('Currency')),
                                                        ])
                                                        ->schema([
                                                            BelongsToSelect::make('service_item_id')
                                                                ->label(__('Service Item'))
                                                                ->relationship('serviceItem', 'name', function ($query, $get) {
                                                                    return $query->where('service_items.service_id', $get('../../service_id'));
                                                                })
                                                                ->searchable()
                                                                ->reactive()
                                                                ->afterStateUpdated(function ($get, $set) {
                                                                    $serviceProvider = ServiceProvider::query()->findOrFail($get('../../service_provider_id'));

                                                                    $serviceProviderItem = ServiceProviderItem::query()
                                                                        ->where('service_provider_id', $serviceProvider->id)
                                                                        ->where('service_item_id', $get('service_item_id'))
                                                                        ->first();

                                                                    if ($serviceProviderItem) {
                                                                        $set('price', $serviceProviderItem->price);
                                                                        $set('price_currency', $serviceProviderItem->price_currency);
                                                                    }else{
                                                                        $set('price', null);
                                                                        $set('price_currency', 'EGP');
                                                                    }
                                                                })
                                                                ->preload()
                                                                ->required(),

                                                            TextInput::make('price')
                                                                ->label(__('Price'))
                                                                ->numeric()
                                                                ->required()
                                                                ->minValue(0),

                                                            Select::make('price_currency')
                                                                ->label(__('Currency'))
                                                                ->options([
                                                                    'EGP' => 'EGP',
                                                                    'USD' => 'USD',
                                                                    'EUR' => 'EUR',
                                                                    'GBP' => 'GBP',
                                                                ])
                                                                ->required(),
                                                        ]),

                                                    BelongsToSelect::make('payment_term_id')
                                                        ->label(__('Payment Term'))
                                                        ->relationship('paymentTerm', 'name')
                                                        ->preload()
                                                        ->searchable(),
                                                ])->columnSpan(function ($record) {
                                                    return $record ? 2 : 3;
                                                }),

                                                Section::make(__('Financial Overview'))
                                                    ->columnSpan(1)
                                                    ->visible(function ($record) {
                                                        return !!$record;
                                                    })
                                                    ->schema(function (PurchaseOrderService|null $record) {
                                                        return [
                                                            TextEntry::make('service_price')
                                                                ->label(__('Services Price'))
                                                                ->getStateUsing(function ($record) {
                                                                    return $record->getPriceLabels('price');
                                                                })
                                                                ->listWithLineBreaks()
                                                                ->bulleted(),

                                                            TextEntry::make('service_paid')
                                                                ->label(__('Paid'))
                                                                ->getStateUsing(function ($record) {
                                                                    return $record->getPriceLabels('paid');
                                                                })
                                                                ->listWithLineBreaks()
                                                                ->bulleted(),

                                                            TextEntry::make('service_remaining')
                                                                ->label(__('Remaining'))
                                                                ->getStateUsing(function ($record) {
                                                                    return $record->getPriceLabels('remaining');
                                                                })
                                                                ->listWithLineBreaks()
                                                                ->bulleted(),

                                                            Actions::make([
                                                                Action::make('view_payments')
                                                                    ->label(__('View Payments'))
                                                                    ->color('success')
                                                                    ->icon(Heroicon::Eye)
                                                                    ->schema([
                                                                        Livewire::make(PaymentsTable::class, [
                                                                            'payable' => $record,
                                                                            'party' => $record?->serviceProvider,
                                                                            'transactionType' => TransactionType::WITHDRAWAL
                                                                        ])->key("payments-table-{$record?->id}"),
                                                                    ])
                                                                    ->action(fn() => null),

                                                            ])->alignEnd(),
                                                        ];
                                                    })
                                            ]),
                                    ]),
                            ]),

                        Tabs\Tab::make(__('Payments'))
                            ->icon(Heroicon::CurrencyDollar)
                            ->visible(function ($record) {
                                return !!$record;
                            })
                            ->schema(function ($record) {
                                return [
                                    Section::make(__('Financial Overview'))
                                        ->columns(3)
                                        ->schema([
                                            TextEntry::make('price')
                                                ->label(__('Products Price'))
                                                ->getStateUsing(function ($record) {
                                                    return $record->getPriceLabels('price');
                                                })
                                                ->listWithLineBreaks()
                                                ->bulleted(),

                                            // paid
                                            TextEntry::make('paid')
                                                ->label(__('Paid'))
                                                ->getStateUsing(function ($record) {
                                                    return $record->getPriceLabels('paid');
                                                })
                                                ->listWithLineBreaks()
                                                ->bulleted(),

                                            // remaining
                                            TextEntry::make('remaining')
                                                ->label(__('Remaining'))
                                                ->getStateUsing(function ($record) {
                                                    return $record->getPriceLabels('remaining');
                                                })
                                                ->listWithLineBreaks()
                                                ->bulleted(),
                                        ]),

                                    Livewire::make(PaymentsTable::class, [
                                        'payable' => $record,
                                        'party' => $record?->supplier,
                                        'transactionType' => TransactionType::WITHDRAWAL,
                                    ])
                                ];
                            }),
                    ])
            ]);
    }

    public static function getSupplierCountryAndCity($get, $set, Component $component)
    {
        $supplierId = $get('data.supplier_id', true);

        if ($supplierId) {
            $supplier = Supplier::query()->findOrFail($supplierId);

            return [
                $supplier->country,
                $supplier->city,
                $supplier->address
            ];
        }

        return [
            null,
            null,
            null,
        ];
    }

    public static function getSupplierPort($get, $set, Component $component)
    {
        $supplierId = $get('data.supplier_id', true);
        $modelOfShipping = $get('data.model_of_shipping', true);
        $portType = $modelOfShipping == OrderModelOfShipping::Air->value ? Airport::class : Seaport::class;

        if ($supplierId) {
            $supplier = Supplier::query()->findOrFail($supplierId);

            $modelPort = $supplier->modelPorts->firstWhere('port_type', '=', $portType);

            return $modelPort?->port;
        }

        return null;
    }
}
