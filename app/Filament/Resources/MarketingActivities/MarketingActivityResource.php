<?php

namespace App\Filament\Resources\MarketingActivities;

use App\Filament\Resources\MarketingActivities\Pages\CreateMarketingActivity;
use App\Filament\Resources\MarketingActivities\Pages\EditMarketingActivity;
use App\Filament\Resources\MarketingActivities\Pages\ListMarketingActivities;
use App\Filament\Resources\MarketingActivities\Schemas\MarketingActivityForm;
use App\Filament\Resources\MarketingActivities\Tables\MarketingActivitiesTable;
use App\Models\MarketingActivity;
use BackedEnum;
use App\Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use UnitEnum;

class MarketingActivityResource extends Resource
{
    protected static ?string $model = MarketingActivity::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedRectangleStack;

    public static function getLabel(): ?string
    {
        return __('Marketing Activity');
    }

    public static function getPluralLabel(): ?string
    {
        return __('Marketing Activities');
    }

    public static function getNavigationGroup(): string|UnitEnum|null
    {
        return __('Marketing');
    }

    public static function form(Schema $schema): Schema
    {
        return MarketingActivityForm::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return MarketingActivitiesTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListMarketingActivities::route('/'),
            'create' => CreateMarketingActivity::route('/create'),
            'edit' => EditMarketingActivity::route('/{record}/edit'),
        ];
    }

    public static function getRecordRouteBindingEloquentQuery(): Builder
    {
        return parent::getRecordRouteBindingEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
