<?php

namespace App\Filament\Resources\MarketingActivities\Tables;

use App\Enums\MarketingActivityAction;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ForceDeleteBulkAction;
use Filament\Actions\RestoreBulkAction;
use Filament\Forms\Components\DatePicker;
use Filament\Tables\Columns\SelectColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TrashedFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class MarketingActivitiesTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('incremental_id')
                    ->label('#')
                    ->sortable(),

                TextColumn::make('name')
                    ->label(__('Name'))
                    ->searchable(),

                TextColumn::make('content')
                    ->label(__('Content'))
                    ->searchable()
                    ->limit(50),

                TextColumn::make('budget')
                    ->label(__('Budget'))
                    ->money(fn ($record) => $record->budget_currency ?? 'EGP')
                    ->sortable(),

                TextColumn::make('marketingPlatform.name')
                    ->label(__('Marketing Platform')),

                SelectColumn::make('action')
                    ->label(__('Action'))
                    ->options(MarketingActivityAction::getSelectOptions()),

                TextColumn::make('created_at')
                    ->label(__('Created At'))
                    ->dateTime()
                    ->sortable(),
            ])
            ->defaultSort('incremental_id', 'desc')
            ->filters([
                TrashedFilter::make(),

                SelectFilter::make('marketing_platform_id')
                    ->label(__('Marketing Platform'))
                    ->relationship('marketingPlatform', 'name'),

                SelectFilter::make('action')
                    ->label(__('Action'))
                    ->options(MarketingActivityAction::getSelectOptions()),

                Filter::make('created_at')
                    ->label(__('Created At'))
                    ->schema([
                        DatePicker::make('created_from')
                            ->label(__('Created From')),
                        DatePicker::make('created_until')
                            ->label(__('Created Until')),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    }),
            ])
            ->recordActions([
                EditAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                    ForceDeleteBulkAction::make(),
                    RestoreBulkAction::make(),
                ]),
            ]);
    }
}
