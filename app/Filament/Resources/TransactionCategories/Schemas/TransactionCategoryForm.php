<?php

namespace App\Filament\Resources\TransactionCategories\Schemas;

use App\Filament\Schemas\Components\Translateable;
use Filament\Schemas\Schema;

class TransactionCategoryForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->columns(1)
            ->components([
                Translateable::make('name')
                    ->label(__('Name'))
                    ->required(),
            ]);
    }
}
