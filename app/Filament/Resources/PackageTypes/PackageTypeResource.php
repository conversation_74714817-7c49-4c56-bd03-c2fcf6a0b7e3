<?php

namespace App\Filament\Resources\PackageTypes;

use App\Filament\Resources\PackageTypes\Pages\CreatePackageType;
use App\Filament\Resources\PackageTypes\Pages\EditPackageType;
use App\Filament\Resources\PackageTypes\Pages\ListPackageTypes;
use App\Filament\Resources\PackageTypes\Schemas\PackageTypeForm;
use App\Filament\Resources\PackageTypes\Tables\PackageTypesTable;
use App\Models\PackageType;
use BackedEnum;
use App\Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use UnitEnum;

class PackageTypeResource extends Resource
{
    protected static ?string $model = PackageType::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedRectangleStack;

    public static function getLabel(): ?string
    {
        return __('Package Type');
    }

    public static function getPluralLabel(): ?string
    {
        return __('Package Types');
    }

    public static function getNavigationGroup(): string|UnitEnum|null
    {
        return __('Master data');
    }

    public static function form(Schema $schema): Schema
    {
        return PackageTypeForm::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return PackageTypesTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListPackageTypes::route('/'),
            'create' => CreatePackageType::route('/create'),
            'edit' => EditPackageType::route('/{record}/edit'),
        ];
    }

    public static function getRecordRouteBindingEloquentQuery(): Builder
    {
        return parent::getRecordRouteBindingEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
