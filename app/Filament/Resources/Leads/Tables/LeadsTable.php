<?php

namespace App\Filament\Resources\Leads\Tables;

use App\Enums\LeadStatus;
use App\Filament\Resources\Clients\ClientResource;
use App\Models\Client;
use Filament\Actions\Action;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ForceDeleteBulkAction;
use Filament\Actions\RestoreBulkAction;
use Filament\Forms\Components\DatePicker;
use Filament\Notifications\Notification;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Columns\SelectColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TrashedFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class LeadsTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('incremental_id')
                    ->label('#')
                    ->sortable(),

                TextColumn::make('name')
                    ->label(__('Name'))
                    ->searchable(),

                TextColumn::make('company_name')
                    ->label(__('Company Name'))
                    ->searchable(),

                SelectColumn::make('status')
                    ->label(__('Status'))
                    ->options(LeadStatus::getSelectOptions())
                    ->disabled(function ($record) {
                        return $record->status === LeadStatus::CONVERTED;
                    }),

                TextColumn::make('email')
                    ->label(__('Email'))
                    ->searchable(),

                TextColumn::make('phone')
                    ->label(__('Phone'))
                    ->searchable(),

                TextColumn::make('country.name')
                    ->label(__('Country')),

                TextColumn::make('leadSource.name')
                    ->label(__('Lead Source')),

                TextColumn::make('assignee.name')
                    ->label(__('Assignee')),

                TextColumn::make('created_at')
                    ->label(__('Created At'))
                    ->dateTime()
                    ->sortable(),
            ])
            ->defaultSort('incremental_id', 'desc')
            ->filters([
                TrashedFilter::make(),

                SelectFilter::make('country_id')
                    ->label(__('Country'))
                    ->relationship('country', 'name'),

                SelectFilter::make('lead_source_id')
                    ->label(__('Lead Source'))
                    ->relationship('leadSource', 'name'),

                SelectFilter::make('status')
                    ->label(__('Status'))
                    ->options(LeadStatus::getSelectOptions()),

                SelectFilter::make('assignee_id')
                    ->label(__('Assignee'))
                    ->relationship('assignee', 'name'),

                Filter::make('created_at')
                    ->schema([
                        DatePicker::make('created_from'),
                        DatePicker::make('created_until'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    }),
            ])
            ->recordActions([
                Action::make('convert')
                    ->label(__('Convert'))
                    ->icon(Heroicon::CheckCircle)
                    ->color('success')
                    ->requiresConfirmation()
                    ->hidden(function ($record) {
                        return $record->status === LeadStatus::CONVERTED;
                    })
                    ->action(function ($record) {
                        try {
                            DB::beginTransaction();

                            $record->update([
                                'status' => LeadStatus::CONVERTED,
                            ]);

                            $client = Client::query()
                                ->create($record->toArray());

                            $client->update([
                                'lead_id' => $record->id,
                            ]);

                            Notification::make('convert_success')
                                ->title(__('Converted successfully'))
                                ->success()
                                ->send();

                            DB::commit();

                            return redirect(ClientResource::getUrl('edit', ['record' => $client]));
                        }catch (\Exception $exception){
                            DB::rollBack();

                            Log::error($exception->getMessage(), [
                                'exception' => $exception->getMessage(),
                                'trace' => $exception->getTraceAsString(),
                            ]);

                            Notification::make('convert_failed')
                                ->title(__('Failed to convert'))
                                ->danger()
                                ->send();
                        }
                    }),

                EditAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                    ForceDeleteBulkAction::make(),
                    RestoreBulkAction::make(),
                ]),
            ]);
    }
}
