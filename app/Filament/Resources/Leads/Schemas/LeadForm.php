<?php

namespace App\Filament\Resources\Leads\Schemas;

use App\Filament\Forms\Components\BelongsToSelect;
use App\Filament\Resources\FollowUps\Schemas\FollowUpForm;
use App\Filament\Schemas\Components\CountryCitySelect;
use App\Filament\Schemas\Components\PhoneField;
use App\Models\FollowUp;
use App\Models\Lead;
use Filament\Actions\Action;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Infolists\Components\TextEntry;
use Filament\Notifications\Notification;
use Filament\Schemas\Components\Grid;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;

class LeadForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Grid::make(2)
                    ->schema([
                        Section::make(__('Lead Information'))
                            ->columnSpan(function ($record) {
                                return $record ? 1 : 2;
                            })
                            ->schema([
                                TextInput::make('name')
                                    ->label(__('Name'))
                                    ->required(),

                                TextInput::make('email')
                                    ->label(__('Email'))
                                    ->email()
                                    ->required(),

                                PhoneField::make(),

                                TextInput::make('company_name')
                                    ->label(__('Company Name'))
                                    ->required(),

                                CountryCitySelect::make()
                                    ->countryFieldName('country_id')
                                    ->cityFieldName('city_id')
                                    ->countryFieldLabel(__('Country'))
                                    ->cityFieldLabel(__('City'))
                                    ->countryRelationshipName('country')
                                    ->cityRelationshipName('city')
                                    ->required(),

                                Textarea::make('address')
                                    ->label(__('Address')),

                                BelongsToSelect::make('lead_source_id')
                                    ->label(__('Lead Source'))
                                    ->relationship('leadSource', 'name')
                                    ->preload()
                                    ->searchable()
                                    ->required(),

                                BelongsToSelect::make('assignee_id')
                                    ->label(__('Assignee'))
                                    ->relationship('assignee', 'name')
                                    ->required(),
                            ]),

                        Section::make(__('Follow Ups'))
                            ->visible(function ($record) {
                                return !!$record;
                            })
                            ->schema(function (?Lead $record) {
                                return [
                                    Action::make('add_follow_up')
                                        ->label(__('Add Follow Up'))
                                        ->icon('heroicon-o-plus')
                                        ->model(FollowUp::class)
                                        ->schema(function ($record) {
                                            return FollowUpForm::configure(new Schema(), $record->id)->getComponents();
                                        })
                                        ->action(function ($data, $record) {
                                            FollowUp::query()
                                                ->create(array_merge($data, [
                                                    'followable_id' => $record->id,
                                                    'followable_type' => $record->getMorphClass(),
                                                ]));

                                            Notification::make()
                                                ->title(__('Follow Up created'))
                                                ->success()
                                                ->send();
                                        }),

                                    ...(
                                    $record?->followUps()?->latest()?->get()
                                        ?->map(function (FollowUp $followUp) {
                                            return Section::make(
                                                $followUp->follow_up_date?->format('Y-m-d') . ' - ' . "({$followUp->creator?->name})" . ' - ' . $followUp->followUpType?->name
                                            )
                                                ->schema([
                                                    TextEntry::make('follow_up_date')
                                                        ->label(__('Follow Up Date'))
                                                        ->state($followUp->follow_up_date)
                                                        ->date(),

                                                    TextEntry::make('situation')
                                                        ->label(__('Situation'))
                                                        ->state($followUp->situation),

                                                    TextEntry::make('next_follow_up_date')
                                                        ->label(__('Next Follow Up Date'))
                                                        ->state($followUp->next_follow_up_date)
                                                        ->date(),

                                                    TextEntry::make('next_situation')
                                                        ->label(__('Next Situation'))
                                                        ->state($followUp->next_situation),
                                                ]);
                                        })?->toArray() ?? []
                                    )
                                ];
                            }),
                    ])
            ])
            ->columns(1);
    }
}
