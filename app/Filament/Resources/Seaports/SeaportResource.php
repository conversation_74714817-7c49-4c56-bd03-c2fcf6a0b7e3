<?php

namespace App\Filament\Resources\Seaports;

use App\Filament\Resources\Seaports\Pages\CreateSeaport;
use App\Filament\Resources\Seaports\Pages\EditSeaport;
use App\Filament\Resources\Seaports\Pages\ListSeaports;
use App\Filament\Resources\Seaports\Schemas\SeaportForm;
use App\Filament\Resources\Seaports\Tables\SeaportsTable;
use App\Models\Seaport;
use BackedEnum;
use App\Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use UnitEnum;

class SeaportResource extends Resource
{
    protected static ?string $model = Seaport::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedRectangleStack;

    public static function getLabel(): ?string
    {
        return __('Seaport');
    }

    public static function getPluralLabel(): ?string
    {
        return __('Seaports');
    }

    public static function getNavigationGroup(): string|UnitEnum|null
    {
        return __('Master data');
    }

    public static function form(Schema $schema): Schema
    {
        return SeaportForm::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return SeaportsTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListSeaports::route('/'),
            'create' => CreateSeaport::route('/create'),
            'edit' => EditSeaport::route('/{record}/edit'),
        ];
    }

    public static function getRecordRouteBindingEloquentQuery(): Builder
    {
        return parent::getRecordRouteBindingEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
