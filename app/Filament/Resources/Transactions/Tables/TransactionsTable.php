<?php

namespace App\Filament\Resources\Transactions\Tables;

use App\Enums\TransactionType;
use App\Filament\Tables\Columns\EnumColumn;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ForceDeleteBulkAction;
use Filament\Actions\RestoreBulkAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TrashedFilter;
use Filament\Tables\Table;

class TransactionsTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('incremental_id')
                    ->label('#')
                    ->sortable(),

                EnumColumn::make('type')
                    ->label(__('Type')),

                TextColumn::make('amount')
                    ->label(__('Amount'))
                    ->money(fn ($record) => $record->amount_currency ?? 'EGP')
                    ->sortable(),

                TextColumn::make('transactionCategory.name')
                    ->label(__('Transaction Category')),

                TextColumn::make('paymentMethod.name')
                    ->label(__('Payment Method')),

                TextColumn::make('payable.raw_title')
                    ->label(__('Payable')),

                TextColumn::make('party.raw_title')
                    ->label(__('Party')),

                TextColumn::make('created_at')
                    ->label(__('Created At'))
                    ->dateTimeFormatted()
                    ->sortable(),
            ])
            ->defaultSort('incremental_id', 'desc')
            ->filters([
                SelectFilter::make('type')
                    ->label(__('Type'))
                    ->options(TransactionType::getSelectOptions()),

                SelectFilter::make('transaction_category_id')
                    ->label(__('Transaction Category'))
                    ->relationship('transactionCategory', 'name')
                    ->preload()
                    ->searchable(),

                SelectFilter::make('payment_method_id')
                    ->label(__('Payment Method'))
                    ->relationship('paymentMethod', 'name')
                    ->preload()
                    ->searchable(),

                TrashedFilter::make(),
            ])
            ->recordActions([
                EditAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                    ForceDeleteBulkAction::make(),
                    RestoreBulkAction::make(),
                ]),
            ]);
    }
}
