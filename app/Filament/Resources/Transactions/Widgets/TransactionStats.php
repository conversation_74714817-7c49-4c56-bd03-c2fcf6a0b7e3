<?php

namespace App\Filament\Resources\Transactions\Widgets;

use App\Enums\TransactionType;
use App\Models\Transaction;
use Filament\Widgets\StatsOverviewWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\HtmlString;

class TransactionStats extends StatsOverviewWidget
{
    protected function getStats(): array
    {
        $stats = Transaction::query()
            ->selectRaw('type, SUM(amount) as total_amount, type, amount_currency')
            ->groupBy('type', 'amount_currency')
            ->get()
            ->groupBy('type')
            ->map(function ($group) {
                return $group->groupBy('amount_currency')->map->sum('total_amount');
            })
            ->map(function ($group, $type) {
                return Stat::make(
                    __('Total ' . TransactionType::from($type)->label()),
                    new HtmlString(
                        $group->map(function ($value, $currency) use ($type) {
                            $color = TransactionType::from($type)->value == TransactionType::DEPOSIT->value ? 'text-success-500' : 'text-danger-500';
                            $numberFormat = number_format($value, 2, '.', ',');
                            return "<span class='text-xl {$color}'>{$numberFormat} {$currency}</span>";
                        })->implode('<br>')
                    )
                );
            })->toArray();

        $balanceStats = Transaction::query()
            ->selectRaw("
                SUM(
                    CASE
                        WHEN type = ? THEN amount
                        ELSE -amount
                    END
                ) as total_amount,
                amount_currency
            ", [TransactionType::DEPOSIT->value])
            ->groupBy('amount_currency')
            ->get()
            ->groupBy('amount_currency')
            ->map(function ($group) {
                return $group->sum('total_amount');
            });

        $balanceStats = new Stat(
            __('Balance'),
            new HtmlString(
                $balanceStats->map(function ($value, $currency) {
                    $numberFormat = number_format($value, 2, '.', ',');
                    return "<span class='text-xl'>{$numberFormat} {$currency}</span>";
                })->implode('<br>')
            )
        );

        return array_merge($stats, [$balanceStats]);
    }
}
