<?php

namespace App\Filament\Resources\Transactions\Schemas;

use App\Enums\TransactionType;
use App\Filament\Forms\Components\BelongsToSelect;
use App\Filament\Schemas\Components\PriceField;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Schemas\Schema;

class TransactionForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->columns(1)
            ->components([
                Select::make('type')
                    ->label(__('Type'))
                    ->options(TransactionType::getSelectOptions())
                    ->default(TransactionType::WITHDRAWAL->value)
                    ->required(),

                PriceField::make()
                    ->priceFieldName('amount')
                    ->currencyFieldName('amount_currency')
                    ->priceFieldLabel(__('Amount'))
                    ->currencyFieldLabel(__('Amount Currency'))
                    ->required(),

                BelongsToSelect::make('transaction_category_id')
                    ->label(__('Transaction Category'))
                    ->relationship('transactionCategory', 'name')
                    ->preload()
                    ->searchable(),

                BelongsToSelect::make('payment_method_id')
                    ->label(__('Payment Method'))
                    ->relationship('paymentMethod', 'name')
                    ->preload()
                    ->searchable(),

                FileUpload::make('attachments')
                    ->label(__('Attachments'))
                    ->multiple(),

                Textarea::make('notes')
                    ->label(__('Notes')),
            ]);
    }
}
