<?php

namespace App\Filament\Resources\Suppliers\Schemas;

use App\Filament\Forms\Components\BelongsToSelect;
use App\Filament\Schemas\Components\CountryCitySelect;
use App\Filament\Schemas\Components\PhoneField;
use App\Models\Airport;
use App\Models\Seaport;
use Filament\Forms\Components\MorphToSelect;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Schema;
use Filament\Schemas\Components\Text;
use Filament\Support\Icons\Heroicon;

class SupplierForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('name')
                    ->label(__('Name'))
                    ->required(),

                TextInput::make('email')
                    ->label(__('Email'))
                    ->email()
                    ->required(),

                PhoneField::make(),

                TextInput::make('company_name')
                    ->label(__('Company Name'))
                    ->required(),

                TextInput::make('bank_name')
                    ->label(__('Bank Name')),

                TextInput::make('bank_account_number')
                    ->label(__('Bank Account Number')),

                TextInput::make('iban_number')
                    ->label(__('IBAN Number')),

                TextInput::make('swift_code')
                    ->label(__('SWIFT Code')),

                CountryCitySelect::make()
                    ->countryFieldName('country_id')
                    ->cityFieldName('city_id')
                    ->countryFieldLabel(__('Country'))
                    ->cityFieldLabel(__('City'))
                    ->countryRelationshipName('country')
                    ->cityRelationshipName('city')
                    ->afterCountryFieldUpdated(function ($get, $set) {
                        $set('modelPorts', [
                            'port' => [
                                'type' => null,
                                'id' => null,
                            ],
                        ]);
                    })
                    ->required(),

                Textarea::make('address')
                    ->label(__('Address')),

                Select::make('payment_term_id')
                    ->label(__('Payment Term'))
                    ->relationship('paymentTerm', 'name'),

                Repeater::make('modelPorts')
                    ->label(__('Ports'))
                    ->relationship('modelPorts')
                    ->aboveContent(function ($get) {
                        if (!$get('country_id')) {
                            return Text::make(__('Please select a country first.'))
                                ->icon(Heroicon::ExclamationTriangle)
                                ->color('danger');
                        }

                        return null;
                    })
                    ->disabled(function ($get) {
                        return !$get('country_id');
                    })
                    ->schema([
                        MorphToSelect::make('port')
                            ->label(__('Port'))
                            ->preload()
                            ->searchable()
                            ->types([
                                MorphToSelect\Type::make(Airport::class)
                                    ->titleAttribute('name')
                                    ->modifyOptionsQueryUsing(function ($get, $query, $component) {
                                        $basePath = $component->getBasePath();
                                        $countryId = $get("$basePath.country_id", true);

                                        if ($countryId) {
                                            $query->where('country_id', $countryId);
                                        }

                                        return $query;
                                    }),

                                MorphToSelect\Type::make(Seaport::class)
                                    ->titleAttribute('name')
                                    ->modifyOptionsQueryUsing(function ($get, $query, $component) {
                                        $basePath = $component->getBasePath();
                                        $countryId = $get("$basePath.country_id", true);

                                        if ($countryId) {
                                            $query->where('country_id', $countryId);
                                        }

                                        return $query;
                                    }),
                            ]),
                    ]),
            ])
            ->columns(1);
    }
}
