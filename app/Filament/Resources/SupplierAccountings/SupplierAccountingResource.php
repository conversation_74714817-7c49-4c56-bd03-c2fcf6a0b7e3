<?php

namespace App\Filament\Resources\SupplierAccountings;

use App\Filament\Resources\SupplierAccountings\Pages\CreateSupplierAccounting;
use App\Filament\Resources\SupplierAccountings\Pages\EditSupplierAccounting;
use App\Filament\Resources\SupplierAccountings\Pages\ListSupplierAccountings;
use App\Filament\Resources\SupplierAccountings\Schemas\SupplierAccountingForm;
use App\Filament\Resources\SupplierAccountings\Tables\SupplierAccountingsTable;
use App\Models\Supplier;
use BackedEnum;
use App\Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use UnitEnum;

class SupplierAccountingResource extends Resource
{
    protected static ?string $model = Supplier::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedRectangleStack;

    protected static ?string $recordTitleAttribute = 'name';

    public static function getLabel(): ?string
    {
        return __('Supplier Accounting');
    }

    public static function getPluralLabel(): ?string
    {
        return __('Supplier Accounting');
    }

    public static function getNavigationGroup(): string|UnitEnum|null
    {
        return __('Accounting');
    }

    public static function form(Schema $schema): Schema
    {
        return SupplierAccountingForm::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return SupplierAccountingsTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListSupplierAccountings::route('/'),
            'create' => CreateSupplierAccounting::route('/create'),
            'edit' => EditSupplierAccounting::route('/{record}/edit'),
        ];
    }

    public static function getRecordRouteBindingEloquentQuery(): Builder
    {
        return parent::getRecordRouteBindingEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }

    public static function canCreate(): bool
    {
        return false;
    }

    public static function canDelete(Model $record): bool
    {
        return false;
    }

    public static function canDeleteAny(): bool
    {
        return false;
    }

    public static function canForceDeleteAny(): bool
    {
        return false;
    }

    public static function canForceDelete(Model $record): bool
    {
        return false;
    }

    public static function canEdit(Model $record): bool
    {
        return false;
    }
}
