<?php

namespace App\Filament\Resources\SupplierAccountings\Widgets;

use App\Models\PurchaseOrder;
use App\Models\Supplier;
use Filament\Widgets\StatsOverviewWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\HtmlString;

class SupplierAccountingStats extends StatsOverviewWidget
{
    protected function getStats(): array
    {
        $due = [];
        $paid = [];
        $overdue = [];

        foreach (distinct_currencies() as $currency) {
            $due[$currency] = DB::table('purchase_order_products')
                ->selectRaw('COALESCE(SUM(buy_price * quantity), 0) as value')
                ->where('buy_price_currency', $currency)
                ->whereNull('purchase_order_products.deleted_at')
                ->value('value');

            $paid[$currency] = DB::table('payments')
                ->selectRaw('COALESCE(SUM(amount), 0) as value')
                ->where('payments.payable_type', PurchaseOrder::class)
                ->where('payments.party_type', Supplier::class)
                ->where('payments.amount_currency', $currency)
                ->whereNull('payments.deleted_at')
                ->whereNotNull('payments.paid_at')
                ->value('value');

            $overdue[$currency] = data_get($due, $currency, 0) - data_get($paid, $currency, 0);
        }

        return [
            Stat::make(__('Total'), new HtmlString(
                collect(distinct_currencies())->map(function ($currency) use ($due) {
                    $value = number_format(data_get($due, $currency, 0), 2, '.', ',') . ' ' . $currency;
                    return $value > 0 ? "<span class='text-xl text-primary-500'>{$value}</span>" : null;
                })->filter()->values()->implode('<br>')
            )),

            Stat::make(__('Paid'), new HtmlString(
                collect(distinct_currencies())->map(function ($currency) use ($paid) {
                    $value = number_format(data_get($paid, $currency, 0), 2, '.', ',') . ' ' . $currency;
                    return $value > 0 ? "<span class='text-xl text-success-500'>{$value}</span>" : null;
                })->filter()->values()->implode('<br>')
            )),

            Stat::make(__('Remaining'), new HtmlString(
                collect(distinct_currencies())->map(function ($currency) use ($overdue) {
                    $value = number_format(data_get($overdue, $currency, 0), 2, '.', ',') . ' ' . $currency;
                    return $value > 0 ? "<span class='text-xl text-danger-500'>{$value}</span>" : null;
                })->filter()->values()->implode('<br>')
            )),
        ];
    }
}
