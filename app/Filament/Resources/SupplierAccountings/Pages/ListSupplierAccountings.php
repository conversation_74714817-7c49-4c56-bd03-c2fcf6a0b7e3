<?php

namespace App\Filament\Resources\SupplierAccountings\Pages;

use App\Filament\Resources\SupplierAccountings\SupplierAccountingResource;
use App\Filament\Resources\SupplierAccountings\Widgets\SupplierAccountingStats;
use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ListRecords;

class ListSupplierAccountings extends ListRecords
{
    protected static string $resource = SupplierAccountingResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make()
                ->visible(false),
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            SupplierAccountingStats::class,
        ];
    }
}
