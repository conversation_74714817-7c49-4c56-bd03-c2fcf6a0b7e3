<?php

namespace App\Filament\Resources\SupplierAccountings\Pages;

use App\Filament\Resources\SupplierAccountings\SupplierAccountingResource;
use Filament\Actions\DeleteAction;
use Filament\Actions\ForceDeleteAction;
use Filament\Actions\RestoreAction;
use Filament\Resources\Pages\EditRecord;

class EditSupplierAccounting extends EditRecord
{
    protected static string $resource = SupplierAccountingResource::class;

    protected function getHeaderActions(): array
    {
        return [
            DeleteAction::make(),
            ForceDeleteAction::make(),
            RestoreAction::make(),
        ];
    }
}
