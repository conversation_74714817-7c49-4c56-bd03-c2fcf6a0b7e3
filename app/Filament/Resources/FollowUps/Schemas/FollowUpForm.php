<?php

namespace App\Filament\Resources\FollowUps\Schemas;

use App\Filament\Forms\Components\BelongsToSelect;
use App\Filament\Resources\Leads\Pages\CreateLead;
use App\Filament\Resources\Leads\Pages\EditLead;
use App\Models\FollowUp;
use App\Models\Lead;
use App\Models\Client;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\MorphToSelect;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Schema;
use Filament\Support\Enums\TextSize;

class FollowUpForm
{
    public static function configure(Schema $schema, $leadId = null): Schema
    {
        return $schema
            ->components([
                MorphToSelect::make('followable')
                    ->label(__('Followable'))
                    ->searchable()
                    ->default([
                        'followable_type' => Lead::class,
                        'followable_id' => $leadId,
                    ])
                    ->types([
                        MorphToSelect\Type::make(Lead::class)
                            ->titleAttribute('name'),
                    ])
                    ->disabled()
                    ->required(),

                BelongsToSelect::make('follow_up_type_id')
                    ->label(__('Follow Up Type'))
                    ->relationship('followUpType', 'name')
                    ->searchable()
                    ->preload()
                    ->required(),

                DatePicker::make('follow_up_date')
                    ->label(__('Follow Up Date'))
                    ->required(),

                Textarea::make('situation')
                    ->label(__('Situation')),

                DatePicker::make('next_follow_up_date')
                    ->label(__('Next Follow Up Date')),

                Textarea::make('next_situation')
                    ->label(__('Next Situation')),
            ])
            ->columns(1);
    }
}
