<?php

namespace App\Filament\Resources\FollowUps\Tables;

use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ForceDeleteBulkAction;
use Filament\Actions\RestoreBulkAction;
use Filament\Forms\Components\DatePicker;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TrashedFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class FollowUpsTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('incremental_id')
                    ->label('#')
                    ->sortable(),

                TextColumn::make('followable.raw_title')
                    ->label(__('Lead')),

                TextColumn::make('followUpType.name')
                    ->label(__('Follow Up Type')),

                TextColumn::make('creator.name')
                    ->label(__('Sales Person')),

                TextColumn::make('follow_up_date')
                    ->label(__('Follow Up Date'))
                    ->date()
                    ->sortable(),

                TextColumn::make('situation')
                    ->label(__('Situation'))
                    ->searchable()
                    ->limit(50),

                TextColumn::make('next_follow_up_date')
                    ->label(__('Next Follow Up Date'))
                    ->date()
                    ->sortable(),

                TextColumn::make('next_situation')
                    ->label(__('Next Situation'))
                    ->searchable()
                    ->limit(50),

                TextColumn::make('created_at')
                    ->label(__('Created At'))
                    ->dateTimeFormatted()
                    ->sortable(),
            ])
            ->defaultSort('incremental_id', 'desc')
            ->filters([
                SelectFilter::make('follow_up_type_id')
                    ->label(__('Follow Up Type'))
                    ->relationship('followUpType', 'name')
                    ->preload(),

                Filter::make('follow_up_date')
                    ->label(__('Follow Up Date'))
                    ->schema([
                        DatePicker::make('follow_up_from')
                            ->label(__('Follow Up From')),
                        DatePicker::make('follow_up_until')
                            ->label(__('Follow Up Until')),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['follow_up_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('follow_up_date', '>=', $date),
                            )
                            ->when(
                                $data['follow_up_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('follow_up_date', '<=', $date),
                            );
                    }),

                // next_follow_up_date
                Filter::make('next_follow_up_date')
                    ->label(__('Next Follow Up Date'))
                    ->schema([
                        DatePicker::make('next_follow_up_from')
                            ->label(__('Next Follow Up From')),
                        DatePicker::make('next_follow_up_until')
                            ->label(__('Next Follow Up Until')),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['next_follow_up_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('next_follow_up_date', '>=', $date),
                            )
                            ->when(
                                $data['next_follow_up_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('next_follow_up_date', '<=', $date),
                            );
                    }),

                Filter::make('created_at')
                    ->label(__('Created At'))
                    ->schema([
                        DatePicker::make('created_from')
                            ->label(__('Created From')),
                        DatePicker::make('created_until')
                            ->label(__('Created Until')),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    }),

                TrashedFilter::make(),
            ])
            ->recordActions([
                EditAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                    ForceDeleteBulkAction::make(),
                    RestoreBulkAction::make(),
                ]),
            ]);
    }
}
