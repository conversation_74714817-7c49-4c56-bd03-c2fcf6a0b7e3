<?php

namespace App\Filament\Resources\FinishedProducts;

use App\Enums\ProductType;
use App\Filament\Resources\FinishedProducts\Pages\CreateFinishedProduct;
use App\Filament\Resources\FinishedProducts\Pages\EditFinishedProduct;
use App\Filament\Resources\FinishedProducts\Pages\ListFinishedProducts;
use App\Filament\Resources\FinishedProducts\Schemas\FinishedProductForm;
use App\Filament\Resources\FinishedProducts\Tables\FinishedProductsTable;
use App\Models\Product;
use BackedEnum;
use App\Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use UnitEnum;

class FinishedProductResource extends Resource
{
    protected static ?string $model = Product::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedRectangleStack;

    protected static ?string $recordTitleAttribute = 'name';

    public static function getLabel(): ?string
    {
        return __('Finished Product');
    }

    public static function getPluralLabel(): ?string
    {
        return __('Finished Products');
    }

    public static function getNavigationGroup(): string|UnitEnum|null
    {
        return __('Inventory');
    }

    public static function form(Schema $schema): Schema
    {
        return FinishedProductForm::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return FinishedProductsTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListFinishedProducts::route('/'),
            'create' => CreateFinishedProduct::route('/create'),
            'edit' => EditFinishedProduct::route('/{record}/edit'),
        ];
    }

    public static function getRecordRouteBindingEloquentQuery(): Builder
    {
        return parent::getRecordRouteBindingEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ])->where('type', ProductType::FINISHED->value);
    }
}
