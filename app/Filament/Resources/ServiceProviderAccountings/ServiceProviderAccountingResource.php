<?php

namespace App\Filament\Resources\ServiceProviderAccountings;

use App\Filament\Resources\ServiceProviderAccountings\Pages\CreateServiceProviderAccounting;
use App\Filament\Resources\ServiceProviderAccountings\Pages\EditServiceProviderAccounting;
use App\Filament\Resources\ServiceProviderAccountings\Pages\ListServiceProviderAccountings;
use App\Filament\Resources\ServiceProviderAccountings\Schemas\ServiceProviderAccountingForm;
use App\Filament\Resources\ServiceProviderAccountings\Tables\ServiceProviderAccountingsTable;
use App\Models\ServiceProvider;
use App\Models\ServiceProviderAccounting;
use BackedEnum;
use App\Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use UnitEnum;

class ServiceProviderAccountingResource extends Resource
{
    protected static ?string $model = ServiceProvider::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedRectangleStack;

    protected static ?string $recordTitleAttribute = 'name';

    public static function getLabel(): ?string
    {
        return __('Service Provider Accounting');
    }

    public static function getPluralLabel(): ?string
    {
        return __('Service Provider Accounting');
    }

    public static function getNavigationGroup(): string|UnitEnum|null
    {
        return __('Accounting');
    }

    public static function form(Schema $schema): Schema
    {
        return ServiceProviderAccountingForm::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return ServiceProviderAccountingsTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListServiceProviderAccountings::route('/'),
            'create' => CreateServiceProviderAccounting::route('/create'),
            'edit' => EditServiceProviderAccounting::route('/{record}/edit'),
        ];
    }

    public static function getRecordRouteBindingEloquentQuery(): Builder
    {
        return parent::getRecordRouteBindingEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }

    public static function canCreate(): bool
    {
        return false;
    }

    public static function canDelete(Model $record): bool
    {
        return false;
    }

    public static function canDeleteAny(): bool
    {
        return false;
    }

    public static function canForceDeleteAny(): bool
    {
        return false;
    }

    public static function canForceDelete(Model $record): bool
    {
        return false;
    }

    public static function canEdit(Model $record): bool
    {
        return false;
    }
}
