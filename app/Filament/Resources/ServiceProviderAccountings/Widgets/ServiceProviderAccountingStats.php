<?php

namespace App\Filament\Resources\ServiceProviderAccountings\Widgets;

use App\Models\PurchaseOrder;
use App\Models\PurchaseOrderService;
use App\Models\SellOrderService;
use App\Models\ServiceProvider;
use App\Models\Supplier;
use Filament\Widgets\StatsOverviewWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\HtmlString;

class ServiceProviderAccountingStats extends StatsOverviewWidget
{
    protected function getStats(): array
    {
        $due = [];
        $paid = [];
        $overdue = [];

        foreach (distinct_currencies() as $currency) {
            $due[$currency] = DB::query()
                ->fromSub(function ($q) use ($currency) {
                    $q->from('sell_order_service_items')
                        ->selectRaw('COALESCE(SUM(price),0) as total')
                        ->join('sell_order_services', 'sell_order_service_items.sell_order_service_id', '=', 'sell_order_services.id')
                        ->join('sell_orders', 'sell_order_services.sell_order_id', '=', 'sell_orders.id')
                        ->where('price_currency', $currency)
                        ->whereNull('sell_order_service_items.deleted_at')
                        ->whereNull('sell_orders.deleted_at')
                        ->whereNull('sell_order_services.deleted_at')

                        ->unionAll(
                            DB::table('purchase_order_service_items')
                                ->selectRaw('COALESCE(SUM(price),0) as total')
                                ->join('purchase_order_services', 'purchase_order_service_items.purchase_order_service_id', '=', 'purchase_order_services.id')
                                ->join('purchase_orders', 'purchase_order_services.purchase_order_id', '=', 'purchase_orders.id')
                                ->where('price_currency', $currency)
                                ->whereNull('purchase_order_service_items.deleted_at')
                                ->whereNull('purchase_orders.deleted_at')
                                ->whereNull('purchase_order_services.deleted_at')
                        );
                }, 'u')
                ->selectRaw('SUM(u.total) as total_due')
                ->value('total_due');

            $paid[$currency] = DB::table('payments')
                ->selectRaw('COALESCE(SUM(amount), 0) as value')
                ->where(function ($q) use ($currency) {
                    $q->where('payments.payable_type', SellOrderService::class)
                        ->orWhere('payments.payable_type', PurchaseOrderService::class);

                })
                ->where('payments.party_type', ServiceProvider::class)
                ->where('amount_currency', $currency)
                ->whereNull('payments.deleted_at')
                ->whereNotNull('payments.paid_at')
                ->value('value');

            $overdue[$currency] = data_get($due, $currency, 0) - data_get($paid, $currency, 0);
        }

        return [
            Stat::make(__('Total'), new HtmlString(
                collect(distinct_currencies())->map(function ($currency) use ($due) {
                    $value = number_format(data_get($due, $currency, 0), 2, '.', ',') . ' ' . $currency;
                    return $value > 0 ? "<span class='text-xl text-primary-500'>{$value}</span>" : null;
                })->filter()->values()->implode('<br>')
            )),

            Stat::make(__('Paid'), new HtmlString(
                collect(distinct_currencies())->map(function ($currency) use ($paid) {
                    $value = number_format(data_get($paid, $currency, 0), 2, '.', ',') . ' ' . $currency;
                    return $value > 0 ? "<span class='text-xl text-success-500'>{$value}</span>" : null;
                })->filter()->values()->implode('<br>')
            )),

            Stat::make(__('Remaining'), new HtmlString(
                collect(distinct_currencies())->map(function ($currency) use ($overdue) {
                    $value = number_format(data_get($overdue, $currency, 0), 2, '.', ',') . ' ' . $currency;
                    return $value > 0 ? "<span class='text-xl text-danger-500'>{$value}</span>" : null;
                })->filter()->values()->implode('<br>')
            )),
        ];
    }
}
