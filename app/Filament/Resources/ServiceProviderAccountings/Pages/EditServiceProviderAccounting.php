<?php

namespace App\Filament\Resources\ServiceProviderAccountings\Pages;

use App\Filament\Resources\ServiceProviderAccountings\ServiceProviderAccountingResource;
use Filament\Actions\DeleteAction;
use Filament\Actions\ForceDeleteAction;
use Filament\Actions\RestoreAction;
use Filament\Resources\Pages\EditRecord;

class EditServiceProviderAccounting extends EditRecord
{
    protected static string $resource = ServiceProviderAccountingResource::class;

    protected function getHeaderActions(): array
    {
        return [
            DeleteAction::make(),
            ForceDeleteAction::make(),
            RestoreAction::make(),
        ];
    }
}
