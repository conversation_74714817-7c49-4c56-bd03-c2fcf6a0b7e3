<?php

namespace App\Filament\Resources\ClientAccountings\Pages;

use App\Filament\Resources\ClientAccountings\ClientAccountingResource;
use App\Filament\Resources\ClientAccountings\Widgets\ClientAccountingStats;
use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ListRecords;

class ListClientAccountings extends ListRecords
{
    protected static string $resource = ClientAccountingResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make()
                ->visible(false),
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            ClientAccountingStats::class,
        ];
    }
}
