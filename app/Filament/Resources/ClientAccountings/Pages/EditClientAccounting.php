<?php

namespace App\Filament\Resources\ClientAccountings\Pages;

use App\Filament\Resources\ClientAccountings\ClientAccountingResource;
use Filament\Actions\DeleteAction;
use Filament\Actions\ForceDeleteAction;
use Filament\Actions\RestoreAction;
use Filament\Resources\Pages\EditRecord;

class EditClientAccounting extends EditRecord
{
    protected static string $resource = ClientAccountingResource::class;

    protected function getHeaderActions(): array
    {
        return [
            DeleteAction::make(),
            ForceDeleteAction::make(),
            RestoreAction::make(),
        ];
    }
}
