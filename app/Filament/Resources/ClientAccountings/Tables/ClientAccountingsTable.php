<?php

namespace App\Filament\Resources\ClientAccountings\Tables;

use App\Filament\Widgets\InvoicesTable;
use App\Models\Client;
use Filament\Actions\Action;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ForceDeleteBulkAction;
use Filament\Actions\RestoreBulkAction;
use Filament\Schemas\Components\Livewire;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\TrashedFilter;
use Filament\Tables\Table;

class ClientAccountingsTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('incremental_id')
                    ->label('#')
                    ->sortable(),

                TextColumn::make('name')
                    ->label(__('Client'))
                    ->searchable(),

                TextColumn::make('company_name')
                    ->label(__('Company Name'))
                    ->searchable(),

                TextColumn::make('due')
                    ->label(__('Due'))
                    ->listWithLineBreaks()
                    ->bulleted()
                    ->getStateUsing(function (Client $record) {
                        return $record->getPriceLabels('due');
                    }),

                TextColumn::make('collected')
                    ->label(__('Paid'))
                    ->listWithLineBreaks()
                    ->bulleted()
                    ->getStateUsing(function (Client $record) {
                        return $record->getPriceLabels('collected');
                    }),

                TextColumn::make('remaining')
                    ->label(__('Remaining'))
                    ->listWithLineBreaks()
                    ->bulleted()
                    ->getStateUsing(function (Client $record) {
                        return $record->getPriceLabels('remaining');
                    }),

            ])
            ->filters([
                TrashedFilter::make(),
            ])
            ->recordActions([
                Action::make('view_invoices')
                    ->color('danger')
                    ->label(__('Invoices'))
                    ->icon(Heroicon::PaperClip)
                    ->schema(function ($record) {
                        return [
                            Livewire::make(InvoicesTable::class, [
                                'party' => $record,
                            ])
                        ];
                    }),

                EditAction::make()
                    ->visible(false),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                    ForceDeleteBulkAction::make(),
                    RestoreBulkAction::make(),
                ])->visible(false),
            ]);
    }
}
