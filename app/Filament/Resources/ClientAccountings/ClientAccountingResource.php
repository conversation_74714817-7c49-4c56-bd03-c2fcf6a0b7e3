<?php

namespace App\Filament\Resources\ClientAccountings;

use App\Filament\Resources\ClientAccountings\Pages\CreateClientAccounting;
use App\Filament\Resources\ClientAccountings\Pages\EditClientAccounting;
use App\Filament\Resources\ClientAccountings\Pages\ListClientAccountings;
use App\Filament\Resources\ClientAccountings\Schemas\ClientAccountingForm;
use App\Filament\Resources\ClientAccountings\Tables\ClientAccountingsTable;
use App\Models\Client;
use BackedEnum;
use App\Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use UnitEnum;

class ClientAccountingResource extends Resource
{
    protected static ?string $model = Client::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedRectangleStack;

    protected static ?string $recordTitleAttribute = 'name';

    public static function getLabel(): ?string
    {
        return __('Client Accounting');
    }

    public static function getPluralLabel(): ?string
    {
        return __('Client Accounting');
    }

    public static function getNavigationGroup(): string|UnitEnum|null
    {
        return __('Accounting');
    }

    public static function form(Schema $schema): Schema
    {
        return ClientAccountingForm::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return ClientAccountingsTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListClientAccountings::route('/'),
            'create' => CreateClientAccounting::route('/create'),
            'edit' => EditClientAccounting::route('/{record}/edit'),
        ];
    }

    public static function getRecordRouteBindingEloquentQuery(): Builder
    {
        return parent::getRecordRouteBindingEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }

    public static function canCreate(): bool
    {
        return false;
    }

    public static function canDelete(Model $record): bool
    {
        return false;
    }

    public static function canDeleteAny(): bool
    {
        return false;
    }

    public static function canForceDeleteAny(): bool
    {
        return false;
    }

    public static function canForceDelete(Model $record): bool
    {
        return false;
    }

    public static function canEdit(Model $record): bool
    {
        return false;
    }
}
