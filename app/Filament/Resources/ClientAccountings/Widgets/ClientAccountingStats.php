<?php

namespace App\Filament\Resources\ClientAccountings\Widgets;

use App\Models\Client;
use App\Models\SellOrder;
use Filament\Widgets\StatsOverviewWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\HtmlString;

class ClientAccountingStats extends StatsOverviewWidget
{
    protected function getStats(): array
    {
        $due = [];
        $paid = [];
        $overdue = [];

        foreach (distinct_currencies() as $currency) {
            $due[$currency] = DB::table('sell_order_products')
                ->selectRaw('COALESCE(SUM(sell_price * quantity), 0) as value')
                ->where('sell_price_currency', $currency)
                ->whereNull('sell_order_products.deleted_at')
                ->value('value');

            $paid[$currency] = DB::table('payments')
                ->selectRaw('COALESCE(SUM(amount), 0) as value')
                ->where('payments.payable_type', SellOrder::class)
                ->where('payments.party_type', Client::class)
                ->where('payments.amount_currency', $currency)
                ->whereNull('payments.deleted_at')
                ->whereNotNull('payments.paid_at')
                ->value('value');

            $overdue[$currency] = data_get($due, $currency, 0) - data_get($paid, $currency, 0);
        }

        return [
            Stat::make(__('Total'), new HtmlString(
                collect(distinct_currencies())->map(function ($currency) use ($due) {
                    $value = number_format(data_get($due, $currency, 0), 2, '.', ',') . ' ' . $currency;
                    return $value > 0 ? "<span class='text-xl text-primary-500'>{$value}</span>" : null;
                })->filter()->values()->implode('<br>')
            )),

            Stat::make(__('Paid'), new HtmlString(
                collect(distinct_currencies())->map(function ($currency) use ($paid) {
                    $value = number_format(data_get($paid, $currency, 0), 2, '.', ',') . ' ' . $currency;
                    return $value > 0 ? "<span class='text-xl text-success-500'>{$value}</span>" : null;
                })->filter()->values()->implode('<br>')
            )),

            Stat::make(__('Remaining'), new HtmlString(
                collect(distinct_currencies())->map(function ($currency) use ($overdue) {
                    $value = number_format(data_get($overdue, $currency, 0), 2, '.', ',') . ' ' . $currency;
                    return $value > 0 ? "<span class='text-xl text-danger-500'>{$value}</span>" : null;
                })->filter()->values()->implode('<br>')
            )),
        ];
    }
}
