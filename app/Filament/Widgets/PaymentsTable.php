<?php

namespace App\Filament\Widgets;

use App\Enums\TransactionType;
use App\Filament\Resources\Payments\Schemas\PaymentForm;
use App\Models\Payment;
use Filament\Actions\Action;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Notifications\Notification;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class PaymentsTable extends TableWidget
{
    public ?Model $payable = null;

    public ?Model $party = null;

    public $transactionType = TransactionType::WITHDRAWAL;

    public function table(Table $table): Table
    {
        return $table
            ->query(function (): Builder {
                return Payment::query()
                    ->when($this->payable, function (Builder $query) {
                        $query->whereMorphedTo('payable', $this->payable);
                    })
                    ->when($this->party, function (Builder $query) {
                        $query->whereMorphedTo('party', $this->party);
                    });
            })
            ->columns(\App\Filament\Resources\Payments\Tables\PaymentsTable::configure($table)->getColumns())
            ->defaultSort('incremental_id', 'desc')
            ->filters([
                SelectFilter::make('payment_method_id')
                    ->label(__('Payment Method'))
                    ->relationship('paymentMethod', 'name')
                    ->preload(),
            ])
            ->headerActions([
                Action::make('add_payment')
                    ->label(__('Add Payment'))
                    ->icon(Heroicon::CurrencyDollar)
                    ->model(Payment::class)
                    ->schema(function () {
                        return PaymentForm::configure(new Schema())->getComponents();
                    })
                    ->action(function ($data) {
                        Payment::query()
                            ->create(array_merge($data, [
                                'payable_id' => $this->payable->id,
                                'payable_type' => $this->payable->getMorphClass(),
                                'party_id' => $this->party->id,
                                'party_type' => $this->party->getMorphClass(),
                                'transaction_type' => $this->transactionType?->value,
                            ]));

                        Notification::make('payment_created')
                            ->title(__('Payment created'))
                            ->success()
                            ->send();
                    })
            ])
            ->recordActions([
                EditAction::make()
                    ->schema(PaymentForm::configure(new Schema())->getComponents()),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }
}
