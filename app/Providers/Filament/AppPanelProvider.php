<?php

namespace App\Providers\Filament;

use App\Filament\Pages\Tenancy\RegisterCompany;
use App\Filament\Resources\Airports\AirportResource;
use App\Filament\Resources\Cities\CityResource;
use App\Filament\Resources\ClientAccountings\ClientAccountingResource;
use App\Filament\Resources\Clients\ClientResource;
use App\Filament\Resources\Countries\CountryResource;
use App\Filament\Resources\FinishedProducts\FinishedProductResource;
use App\Filament\Resources\FollowUps\FollowUpResource;
use App\Filament\Resources\FollowUpTypes\FollowUpTypeResource;
use App\Filament\Resources\Inventories\InventoryResource;
use App\Filament\Resources\Leads\LeadResource;
use App\Filament\Resources\LeadSources\LeadSourceResource;
use App\Filament\Resources\MarketingActivities\MarketingActivityResource;
use App\Filament\Resources\PackageTypes\PackageTypeResource;
use App\Filament\Resources\PaymentMethods\PaymentMethodResource;
use App\Filament\Resources\PaymentTerms\PaymentTermResource;
use App\Filament\Resources\ProductionOrders\ProductionOrderResource;
use App\Filament\Resources\PurchaseOrders\PurchaseOrderResource;
use App\Filament\Resources\RawProducts\RawProductResource;
use App\Filament\Resources\Seaports\SeaportResource;
use App\Filament\Resources\SellOrders\SellOrderResource;
use App\Filament\Resources\ServiceProviderAccountings\ServiceProviderAccountingResource;
use App\Filament\Resources\ServiceProviders\ServiceProviderResource;
use App\Filament\Resources\Services\ServiceResource;
use App\Filament\Resources\SupplierAccountings\SupplierAccountingResource;
use App\Filament\Resources\Suppliers\SupplierResource;
use App\Filament\Resources\TransactionCategories\TransactionCategoryResource;
use App\Filament\Resources\Transactions\TransactionResource;
use App\Models\Company;
use App\Models\LeadSource;
use App\Models\MarketingPlatform;
use Filament\Forms\Components\FileUpload;
use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\AuthenticateSession;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Infolists\Components\ImageEntry;
use Filament\Infolists\Components\TextEntry;
use Filament\Pages\Dashboard;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Schemas\Components\Component;
use Filament\Support\Colors\Color;
use Filament\Support\Enums\TextSize;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Widgets\AccountWidget;
use Filament\Widgets\FilamentInfoWidget;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;

class AppPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->default()
            ->id('app')
            ->path('app')
            ->login()
            ->colors([
                'primary' => Color::Amber,
            ])
            ->discoverResources(in: app_path('Filament/Resources'), for: 'App\Filament\Resources')
            ->discoverPages(in: app_path('Filament/Pages'), for: 'App\Filament\Pages')
            ->pages([
                Dashboard::class,
            ])
            ->discoverWidgets(in: app_path('Filament/Widgets'), for: 'App\Filament\Widgets')
            ->widgets([
                AccountWidget::class,
                FilamentInfoWidget::class,
            ])
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->registration()
            ->tenant(Company::class)
            ->tenantRegistration(RegisterCompany::class)
            ->databaseNotifications()
            ->databaseNotificationsPolling('60s')
            ->colors([
                'primary' => Color::Blue,
            ])
            ->navigationGroups([
                __('Marketing'),
                __('CRM'),
                __('Sales'),
                __('Purchases'),
                __('Production'),
                __('Inventory'),
                __('Accounting'),
                __('Finance'),
                __('Services'),
                __('Master data'),
            ])
            ->viteTheme('resources/css/filament/app/theme.css')
            ->authMiddleware([
                Authenticate::class,
            ]);
    }

    public static function navigationSort(): array
    {
        return [
            MarketingActivityResource::class,
            MarketingPlatform::class,

            LeadResource::class,
            FollowUpResource::class,
            FollowUpTypeResource::class,
            LeadSourceResource::class,

            SellOrderResource::class,
            ClientResource::class,

            PurchaseOrderResource::class,
            SupplierResource::class,

            ProductionOrderResource::class,

            FinishedProductResource::class,
            RawProductResource::class,
            InventoryResource::class,

            ClientAccountingResource::class,
            SupplierAccountingResource::class,
            ServiceProviderAccountingResource::class,

            // Finance
            TransactionCategoryResource::class,
            TransactionResource::class,

            ServiceResource::class,
            ServiceProviderResource::class,

            // Master data
            CountryResource::class,
            CityResource::class,
            AirportResource::class,
            SeaportResource::class,
            PaymentTermResource::class,
            PackageTypeResource::class,
            PaymentMethodResource::class,
        ];
    }

    public function boot()
    {
        FileUpload::configureUsing(fn (FileUpload $fileUpload) => $fileUpload
            ->visibility('public'));

        ImageColumn::configureUsing(fn (ImageColumn $imageColumn) => $imageColumn
            ->visibility('public'));

        ImageEntry::configureUsing(fn (ImageEntry $imageEntry) => $imageEntry
            ->visibility('public'));

        TextColumn::macro('dateTimeFormatted', function () {
            return $this->dateTime('d-M-Y H:i A');
        });

        Component::macro('getBasePath', function () {
            return $this->evaluate(function (Component $component) {
                $statePath = $component->getStatePath();
                return str($statePath)->beforeLast('data.') . 'data';
            });
        });

        TextEntry::configureUsing(function (TextEntry $textEntry) {
            $textEntry->color('primary')
                ->size(TextSize::Large);
        });
    }
}
