<?php

namespace App\Models;

use App\Models\Concerns\BelongsToCompany;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Translatable\HasTranslations;
use App\Models\Concerns\CommonBetweenModels;

class PaymentMethod extends Model
{
    use SoftDeletes, BelongsToCompany, HasTranslations, CommonBetweenModels;

    protected $fillable = [
        'name',
        'handle',
        'company_id',
        'incremental_id',
    ];

    public $translatable = ['name'];
}
