<?php

namespace App\Models;

use App\Models\Concerns\BelongsToCompany;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Concerns\CommonBetweenModels;

class ModelPort extends Model
{
    use SoftDeletes, BelongsToCompany, CommonBetweenModels;

    protected $fillable = [
        'model_id',
        'model_type',
        'port_id',
        'port_type',
        'company_id',
        'incremental_id',
    ];

    public function model(): MorphTo
    {
        return $this->morphTo();
    }

    public function port(): MorphTo
    {
        return $this->morphTo();
    }
}
