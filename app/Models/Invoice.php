<?php

namespace App\Models;

use App\Models\Concerns\BelongsToCompany;
use App\Models\Concerns\HasPrices;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Invoice extends Model
{
    use SoftDeletes, BelongsToCompany, HasPrices;

    protected $fillable = [
        'invoiceable_id',
        'invoiceable_type',
        'party_id',
        'party_type',
        'company_id',
        'incremental_id',
    ];

    protected $with = ['invoiceable'];

    protected static function booted()
    {
        parent::booted();

        static::addGlobalScope('additional_attributes', function ($query) {
            $query->addSelect('invoices.*');

            $currencies = distinct_currencies();

            foreach ($currencies as $currency) {
                $query->selectSub(function ($q) use ($currency) {
                    $q->from('invoice_items')
                        ->selectRaw('COALESCE(SUM(amount * quantity), 0)')
                        ->whereColumn('invoice_items.invoice_id', 'invoices.id')
                        ->where('amount_currency', $currency)
                        ->whereNull('invoice_items.deleted_at');
                }, "total_amount_{$currency}");
            }
        });
    }

    public function status(): Attribute
    {
        return new Attribute(
            get: function () {
                return $this->invoiceable?->payout_status;
            }
        );
    }

    public function invoiceable()
    {
        return $this->morphTo('invoiceable');
    }

    public function party()
    {
        return $this->morphTo('party');
    }

    public function invoiceItems()
    {
        return $this->hasMany(InvoiceItem::class);
    }
}
