<?php

namespace App\Models;

use App\Models\Concerns\BelongsToCompany;
use App\Models\Concerns\HasPrices;
use App\Models\Concerns\PaymentParty;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Concerns\CommonBetweenModels;

class Supplier extends Model
{
    use SoftDeletes, BelongsToCompany, PaymentParty, CommonBetweenModels, HasPrices;

    protected $fillable = [
        'name',
        'email',
        'phone',
        'dial_code',
        'company_name',
        'bank_name',
        'bank_account_number',
        'iban_number',
        'swift_code',
        'country_id',
        'city_id',
        'address',
        'payment_term_id',
        'company_id',
        'incremental_id',
    ];

    protected static function booted()
    {
        parent::booted();

        static::addGlobalScope('additional_attributes', function ($query) {
            $query->addSelect('suppliers.*');

            $currencies = distinct_currencies();

            foreach ($currencies as $currency) {
                $query->selectSub(function ($q) use ($currency) {
                    $q->from('purchase_order_products')
                        ->selectRaw('COALESCE(SUM(buy_price * quantity), 0)')
                        ->join('purchase_orders', 'purchase_order_products.purchase_order_id', '=', 'purchase_orders.id')
                        ->whereColumn('purchase_orders.supplier_id', 'suppliers.id')
                        ->where('buy_price_currency', $currency)
                        ->whereNull('purchase_order_products.deleted_at');
                }, "due_{$currency}");

                $query->selectSub(function ($q) use ($currency) {
                    $q->from('payments')
                        ->selectRaw('COALESCE(SUM(amount), 0)')
                        ->where('payments.payable_type', PurchaseOrder::class)
                        ->where('payments.party_type', Supplier::class)
                        ->whereColumn('payments.party_id', 'suppliers.id')
                        ->where('amount_currency', $currency)
                        ->whereNull('payments.deleted_at')
                        ->whereNotNull('payments.paid_at');
                }, "paid_{$currency}");

                // remaining = due - paid
                $query->selectSub(function ($q) use ($currency) {
                    $q->selectRaw("due_{$currency} - paid_{$currency} AS remaining_{$currency}");
                }, "remaining_{$currency}");
            }
        });
    }

    // rawTitle
    public function rawTitle(): Attribute
    {
        return Attribute::make(
            get: fn() => __('Supplier') . " ({$this->name}) - ({$this->company_name})",
        );
    }

    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class);
    }

    public function city(): BelongsTo
    {
        return $this->belongsTo(City::class);
    }

    public function paymentTerm(): BelongsTo
    {
        return $this->belongsTo(PaymentTerm::class);
    }

    public function modelPorts(): MorphMany
    {
        return $this->morphMany(ModelPort::class, 'model');
    }
}
