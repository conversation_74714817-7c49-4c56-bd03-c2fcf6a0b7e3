<?php

namespace App\Models;

use App\Models\Concerns\BelongsToCompany;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Translatable\HasTranslations;

class TransactionCategory extends Model
{
    use SoftDeletes, BelongsToCompany, HasTranslations;

    protected $fillable = [
        'name',
        'company_id',
        'incremental_id',
    ];

    public $translatable = ['name'];
}
