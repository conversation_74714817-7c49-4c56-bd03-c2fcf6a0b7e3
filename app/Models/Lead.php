<?php

namespace App\Models;

use App\Enums\LeadStatus;
use App\Models\Concerns\BelongsToCompany;
use App\Models\Concerns\HasModelStates;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Concerns\CommonBetweenModels;

class Lead extends Model
{
    use SoftDeletes, BelongsToCompany, CommonBetweenModels, HasModelStates;

    protected $fillable = [
        'name',
        'email',
        'phone',
        'dial_code',
        'company_name',
        'country_id',
        'city_id',
        'address',
        'status',
        'lead_source_id',
        'assignee_id',
        'company_id',
        'incremental_id',
    ];

    protected static function booted()
    {
        parent::booted();

        static::creating(function (Lead $model) {
            $model->status = LeadStatus::NEW->value;
        });
    }

    protected function casts()
    {
        return [
            'status' => LeadStatus::class,
        ];
    }

    public function rawTitle(): Attribute
    {
        return Attribute::make(
            get: fn() => __('Lead') . " ({$this->name}) - ({$this->company_name})",
        );
    }

    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class);
    }

    public function city(): BelongsTo
    {
        return $this->belongsTo(City::class);
    }

    public function leadSource(): BelongsTo
    {
        return $this->belongsTo(LeadSource::class);
    }

    public function assignee(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assignee_id');
    }

    public function followUps(): MorphMany
    {
        return $this->morphMany(FollowUp::class, 'followable');
    }

    public function client()
    {
        return $this->hasOne(Client::class, 'lead_id');
    }

    public function stateAttributes()
    {
        return [
            'status',
        ];
    }
}
