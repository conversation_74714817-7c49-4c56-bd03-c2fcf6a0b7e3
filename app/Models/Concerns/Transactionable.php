<?php

namespace App\Models\Concerns;

use App\Models\Invoice;
use App\Models\Transaction;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;

trait Transactionable
{
    public static function bootTransactionable()
    {
        static::saved(function (Model $model) {
            if (!$model->transaction) {
                if ($model->saveTransactionWhen()) {
                    $model->transaction()->create([
                        'amount' => $model->getTransactionAmount(),
                        'amount_currency' => $model->getTransactionCurrency(),
                        'type' => $model->getTransactionType(),
                        'payable_id' => $model->getTransactionPayable()?->id,
                        'payable_type' => $model->getTransactionPayable()?->getMorphClass(),
                        'party_id' => $model->getTransactionParty()?->id,
                        'party_type' => $model->getTransactionParty()?->getMorphClass(),
                        'attachments' => $model->getTransactionAttachments(),
                        'notes' => $model->getTransactionNotes(),
                    ]);
                }
            }else{
                $model->transaction->update([
                    'amount' => $model->getTransactionAmount(),
                    'amount_currency' => $model->getTransactionCurrency(),
                    'type' => $model->getTransactionType(),
                    'payable_id' => $model->getTransactionPayable()?->id,
                    'payable_type' => $model->getTransactionPayable()?->getMorphClass(),
                    'party_id' => $model->getTransactionParty()?->id,
                    'party_type' => $model->getTransactionParty()?->getMorphClass(),
                    'attachments' => $model->getTransactionAttachments(),
                    'notes' => $model->getTransactionNotes(),
                ]);
            }
        });

        static::deleted(function (Model $model) {
            $model->transaction?->delete();
        });

        static::restored(function (Model $model) {
            $model->transaction()->withTrashed()->first()?->restore();
        });
    }

    public function transaction()
    {
        return $this->morphOne(Transaction::class, 'transactionable');
    }

    abstract public function getTransactionAmount(): float|int;

    abstract public function getTransactionCurrency(): ?string;

    abstract public function getTransactionType(): ?string;

    abstract public function getTransactionPayable(): ?Model;

    abstract public function getTransactionParty(): ?Model;

    abstract public function getTransactionAttachments(): ?array;

    abstract public function getTransactionNotes(): ?string;

    abstract public function saveTransactionWhen(): bool;
}
