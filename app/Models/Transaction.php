<?php

namespace App\Models;

use App\Enums\TransactionType;
use App\Models\Concerns\BelongsToCompany;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Transaction extends Model
{
    use SoftDeletes, BelongsToCompany;

    protected $fillable = [
        'type',
        'amount',
        'amount_currency',
        'payable_id',
        'payable_type',
        'party_id',
        'party_type',
        'transactionable_id',
        'transactionable_type',
        'transaction_category_id',
        'payment_method_id',
        'creator_id',
        'creator_type',
        'attachments',
        'notes',
        'company_id',
        'incremental_id',
    ];

    protected function casts(): array
    {
        return [
            'attachments' => 'array',
            'type' => TransactionType::class,
        ];
    }

    protected static function booted()
    {
        parent::booted();

        // creator
        static::creating(function (Transaction $model) {
            $model->creator_id = auth()->id();
            $model->creator_type = auth()->user()->getMorphClass();
        });
    }

    public function payable()
    {
        return $this->morphTo();
    }

    public function party()
    {
        return $this->morphTo();
    }

    public function transactionable()
    {
        return $this->morphTo();
    }

    public function creator()
    {
        return $this->morphTo();
    }

    public function transactionCategory()
    {
        return $this->belongsTo(TransactionCategory::class);
    }

    public function paymentMethod()
    {
        return $this->belongsTo(PaymentMethod::class);
    }
}
