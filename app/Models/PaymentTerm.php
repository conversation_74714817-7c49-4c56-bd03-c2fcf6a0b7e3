<?php

namespace App\Models;

use App\Models\Concerns\BelongsToCompany;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Concerns\CommonBetweenModels;

class PaymentTerm extends Model
{
    use SoftDeletes, BelongsToCompany, CommonBetweenModels;

    protected $fillable = [
        'name',
        'handle',
        'company_id',
        'incremental_id',
    ];
}
