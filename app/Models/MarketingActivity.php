<?php

namespace App\Models;

use App\Enums\MarketingActivityAction;
use App\Models\Concerns\BelongsToCompany;
use App\Models\Concerns\CommonBetweenModels;
use App\Models\Concerns\HasModelStates;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Translatable\HasTranslations;

class MarketingActivity extends Model
{
    use SoftDeletes, BelongsToCompany, HasTranslations, CommonBetweenModels, HasModelStates;

    protected $fillable = [
        'name',
        'content',
        'budget',
        'budget_currency',
        'image',
        'action',
        'marketing_platform_id',
        'company_id',
        'incremental_id',
    ];

    public $translatable = ['content'];

    protected static function booted()
    {
        parent::booted();

        static::creating(function (MarketingActivity $model) {
            $model->action = MarketingActivityAction::PENDING->value;
        });
    }

    protected function casts()
    {
        return [
            'action' => MarketingActivityAction::class
        ];
    }

    public function marketingPlatform(): BelongsTo
    {
        return $this->belongsTo(MarketingPlatform::class);
    }

    function stateAttributes()
    {
        return [
            'action',
        ];
    }
}
