<?php

namespace App\Models;

use App\Models\Concerns\BelongsToCompany;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Concerns\CommonBetweenModels;

class FollowUp extends Model
{
    use SoftDeletes, BelongsToCompany, CommonBetweenModels;

    protected $fillable = [
        'notes',
        'follow_up_date',
        'situation',
        'next_follow_up_date',
        'next_situation',
        'creator_id',
        'creator_type',
        'followable_id',
        'followable_type',
        'follow_up_type_id',
        'company_id',
        'incremental_id',
    ];

    protected static function booted()
    {
        parent::booted();

        // creator
        static::creating(function (FollowUp $model) {
            $model->creator_id = auth()->id();
            $model->creator_type = auth()->user()->getMorphClass();
        });
    }

    protected function casts(): array
    {
        return [
            'follow_up_date' => 'datetime',
        ];
    }

    public function followable(): MorphTo
    {
        return $this->morphTo();
    }

    public function creator(): MorphTo
    {
        return $this->morphTo();
    }

    public function followUpType(): BelongsTo
    {
        return $this->belongsTo(FollowUpType::class);
    }
}
