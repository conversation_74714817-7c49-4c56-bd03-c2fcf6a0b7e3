<?php

namespace App\Models;

use App\Models\Concerns\BelongsToCompany;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Concerns\CommonBetweenModels;

class SellOrderServiceProvider extends Model
{
    use SoftDeletes, BelongsToCompany, CommonBetweenModels;

    protected $fillable = [
        'sell_order_id',
        'service_provider_id',
    ];

    public function sellOrder(): BelongsTo
    {
        return $this->belongsTo(SellOrder::class);
    }

    public function serviceProvider(): BelongsTo
    {
        return $this->belongsTo(ServiceProvider::class);
    }

    public function sellOrderServiceItems(): HasMany
    {
        return $this->hasMany(SellOrderServiceItem::class);
    }
}
