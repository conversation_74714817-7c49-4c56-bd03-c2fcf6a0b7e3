<?php

namespace App\Models;

use App\Models\Concerns\BelongsToCompany;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;

class InvoiceItem extends Model
{
    use SoftDeletes, BelongsToCompany;

    protected $fillable = [
        'invoice_id',
        'itemable_id',
        'itemable_type',
        'amount',
        'amount_currency',
        'quantity',
        'company_id',
        'incremental_id',
    ];

    protected static function booted()
    {
        parent::booted();

        static::addGlobalScope('additional_attributes', function ($query) {
            $query->addSelect([
                'invoice_items.*',
                'total_amount' => DB::raw('ROUND(amount * quantity, 2) AS total_amount')
            ]);
        });
    }

    public function invoice()
    {
        return $this->belongsTo(Invoice::class);
    }

    public function itemable()
    {
        return $this->morphTo();
    }
}
