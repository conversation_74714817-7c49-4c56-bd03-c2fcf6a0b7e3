<?php

namespace App\Models;

use App\Models\Concerns\BelongsToCompany;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Translatable\HasTranslations;
use App\Models\Concerns\CommonBetweenModels;

class Country extends Model
{
    use SoftDeletes, BelongsToCompany, HasTranslations, CommonBetweenModels;

    protected $fillable = [
        'name',
        'code',
        'company_id',
        'incremental_id',
    ];

    public $translatable = ['name'];

    public function cities(): HasMany
    {
        return $this->hasMany(City::class);
    }

    public function airports(): HasMany
    {
        return $this->hasMany(Airport::class);
    }

    public function seaports(): HasMany
    {
        return $this->hasMany(Seaport::class);
    }
}
