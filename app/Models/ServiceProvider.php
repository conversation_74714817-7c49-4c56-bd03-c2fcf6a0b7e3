<?php

namespace App\Models;

use App\Models\Concerns\BelongsToCompany;
use App\Models\Concerns\HasPrices;
use App\Models\Concerns\PaymentParty;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Concerns\CommonBetweenModels;
use Illuminate\Support\Facades\DB;

class ServiceProvider extends Model
{
    use SoftDeletes, BelongsToCompany, HasPrices, PaymentParty, CommonBetweenModels;

    protected $fillable = [
        'name',
        'email',
        'dial_code',
        'phone',
        'company_name',
        'address',
        'service_id',
        'country_id',
        'city_id',
        'payment_term_id',
        'bank_name',
        'bank_account_number',
        'iban_number',
        'swift_code',
        'company_id',
        'incremental_id',
    ];

    protected static function booted()
    {
        parent::booted();

        static::addGlobalScope('additional_attributes', function ($query) {
            $query->addSelect('service_providers.*');

            $currencies = distinct_currencies();

            foreach ($currencies as $currency) {
                $query->selectSub(function ($q) use ($currency) {
                    $q->from(function ($sub) use ($currency) {
                        $sub->selectRaw('COALESCE(SUM(price),0) as total')
                            ->from('sell_order_service_items')
                            ->join('sell_order_services', 'sell_order_service_items.sell_order_service_id', '=', 'sell_order_services.id')
                            ->join('sell_orders', 'sell_order_services.sell_order_id', '=', 'sell_orders.id')
                            ->whereColumn('sell_order_services.service_provider_id', 'service_providers.id')
                            ->where('price_currency', $currency)
                            ->whereNull('sell_order_service_items.deleted_at')
                            ->whereNull('sell_orders.deleted_at')
                            ->whereNull('sell_order_services.deleted_at')

                            ->unionAll(
                                DB::table('purchase_order_service_items')
                                    ->selectRaw('COALESCE(SUM(price),0) as total')
                                    ->join('purchase_order_services', 'purchase_order_service_items.purchase_order_service_id', '=', 'purchase_order_services.id')
                                    ->join('purchase_orders', 'purchase_order_services.purchase_order_id', '=', 'purchase_orders.id')
                                    ->whereColumn('purchase_order_services.service_provider_id', 'service_providers.id')
                                    ->where('price_currency', $currency)
                                    ->whereNull('purchase_order_service_items.deleted_at')
                                    ->whereNull('purchase_orders.deleted_at')
                                    ->whereNull('purchase_order_services.deleted_at')
                            );
                    }, 't')
                        ->selectRaw('SUM(total)');
                }, "due_{$currency}");

                $query->selectSub(function ($q) use ($currency) {
                    $q->from('payments')
                        ->selectRaw('COALESCE(SUM(amount), 0)')
                        ->where(function ($q) use ($currency) {
                            $q->where('payments.payable_type', SellOrderService::class)
                                ->orWhere('payments.payable_type', PurchaseOrderService::class);

                        })
                        ->where('payments.party_type', ServiceProvider::class)
                        ->whereColumn('payments.party_id', 'service_providers.id')
                        ->where('amount_currency', $currency)
                        ->whereNull('payments.deleted_at')
                        ->whereNotNull('payments.paid_at');
                }, "paid_{$currency}");

                // remaining = due - paid
                $query->selectSub(function ($q) use ($currency) {
                    $q->selectRaw("due_{$currency} - paid_{$currency} AS remaining_{$currency}");
                }, "remaining_{$currency}");
            }
        });
    }

    // rawTitle
    public function rawTitle(): Attribute
    {
        return Attribute::make(
            get: fn() => __('Service Provider') . " ({$this->name}) - ({$this->company_name})",
        );
    }

    public function service(): BelongsTo
    {
        return $this->belongsTo(Service::class);
    }

    public function serviceProviderItems(): HasMany
    {
        return $this->hasMany(ServiceProviderItem::class);
    }

    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class);
    }

    public function city(): BelongsTo
    {
        return $this->belongsTo(City::class);
    }

    public function paymentTerm(): BelongsTo
    {
        return $this->belongsTo(PaymentTerm::class);
    }

    public function sellOrderServiceItems(): HasMany
    {
        return $this->hasMany(SellOrderServiceItem::class);
    }
}
